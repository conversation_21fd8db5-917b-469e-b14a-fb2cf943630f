# API Latest Incidents - Tài liệu hướng dẫn

## Tổng quan
API này được tạo để lấy 3 vụ việc mới nhất từ trường `metrics` của các báo cáo có `jobType` với `quickReportTemplate.chartTypes` ch<PERSON><PERSON> "highlight".

**Logic lấy dữ liệu:**
1. <PERSON><PERSON>u báo cáo mới nhất có ≥ 3 vụ việc trong `metrics` → chỉ lấy 3 vụ mới nhất từ báo cáo đó
2. Nếu báo cáo mới nhất có < 3 vụ việc → lấy tất cả vụ việc từ báo cáo đó, sau đó lấy thêm từ các báo cáo tiếp theo cho đến khi đủ 3 vụ việc
3. Chỉ lấy các metric có `needsDetails = true` trong JobType configuration

API hỗ trợ filter theo khu vực thông qua query parameter.

## Thông tin endpoint

- **URL**: `/api/v1.0/statistics/latest-incidents`
- **Method**: `POST`
- **Authentication**: Yêu cầu token trong header
- **Content-Type**: `application/json`

## Request Headers
```
token: your-auth-token
Content-Type: application/json
```

## Request Body

### Parameters
| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| `area` | ObjectId | Không | ID khu vực để filter vụ việc theo khu vực cụ thể |

### Ví dụ Request Body

#### 1. Lấy tất cả vụ việc (không filter)
```json
{}
```

#### 2. Filter theo khu vực
```json
{
  "area": "507f1f77bcf86cd799439011"
}
```

## Response Format

### Success Response (Code: 200)
```json
{
  "code": 200,
  "data": {
    "incidents": [
      {
        "_id": "report_id_metric_key_1",
        "title": "Tên metric #1",
        "description": "Vụ việc Tên metric từ báo cáo: Tiêu đề báo cáo",
        "reportId": "report_id",
        "reportType": "quick",
        "caseCode": "CASE001",
        "workStatus": "in_progress",
        "status": "submitted",
        "metricKey": "incident_count",
        "metricLabel": "Số vụ việc",
        "metricValue": 5,
        "incidentIndex": 1,
        "jobType": {
          "_id": "jobtype_id",
          "name": "Tên loại công việc",
          "description": "Mô tả loại công việc",
          "chartTypes": ["highlight"]
        },
        "createdBy": {
          "_id": "user_id",
          "name": "Tên người tạo báo cáo",
          "idNumber": "123456",
          "units": [
            {
              "_id": "unit_id",
              "name": "Tên đơn vị"
            }
          ],
          "areas": [
            {
              "_id": "area_id",
              "name": "Tên khu vực",
              "level": 1
            }
          ]
        },
        "unit": {
          "_id": "unit_id",
          "name": "Tên đơn vị"
        },
        "location": {
          "address": "Địa chỉ cụ thể",
          "coordinates": [105.123456, 21.654321],
          "areas": [
            {
              "_id": "area_id",
              "name": "Tên khu vực",
              "level": 2
            }
          ]
        },
        "incidentTime": 1640995200000,
        "createdAt": 1640995200000,
        "updatedAt": 1640995200000,
        "timeAgo": "2 giờ trước"
      }
    ],
    "total": 1,
    "maxLimit": 3,
    "filter": {
      "area": "507f1f77bcf86cd799439011",
      "chartType": "highlight"
    },
    "generatedAt": 1640995200000
  }
}
```

### Error Response

#### 1. Tham số không hợp lệ (Code: 400)
```json
{
  "code": 400,
  "message": {
    "head": "Thông báo",
    "body": "Bạn vui lòng kiểm tra lại dữ liệu vừa nhập. Xin cảm ơn."
  }
}
```

#### 2. Lỗi hệ thống (Code: 500)
```json
{
  "code": 500,
  "message": {
    "head": "Thông báo",
    "body": "Hệ thống đang bận vui lòng thử lại"
  }
}
```

#### 3. Token hết hạn (Code: 1993)
```json
{
  "code": 1993,
  "message": {
    "head": "Thông báo",
    "body": "Token đã hết hạn, vui lòng đăng nhập lại"
  }
}
```

## Response Fields Giải thích

### Incidents Array
- `_id`: ID duy nhất của vụ việc (format: reportId_metricKey_index)
- `title`: Tiêu đề vụ việc (từ metric label)
- `description`: Mô tả chi tiết vụ việc
- `reportId`: ID của báo cáo gốc
- `reportType`: Loại báo cáo ("quick" hoặc "detail")
- `caseCode`: Mã vụ việc (có thể null)
- `workStatus`: Trạng thái công việc
- `status`: Trạng thái báo cáo
- `metricKey`: Key của metric trong báo cáo
- `metricLabel`: Nhãn hiển thị của metric
- `metricValue`: Tổng giá trị của metric
- `incidentIndex`: Số thứ tự vụ việc trong metric
- `jobType`: Thông tin loại công việc
- `createdBy`: Thông tin người tạo báo cáo
- `unit`: Thông tin đơn vị
- `location`: Thông tin địa điểm (có thể null)
- `incidentTime`: Timestamp xảy ra vụ việc (từ details.time hoặc createdAt)
- `createdAt`: Timestamp tạo báo cáo
- `updatedAt`: Timestamp cập nhật cuối
- `timeAgo`: Chuỗi mô tả thời gian dễ đọc

### Summary Data
- `total`: Số lượng vụ việc trả về
- `maxLimit`: Giới hạn tối đa (luôn là 3)
- `filter`: Thông tin filter đã áp dụng
- `generatedAt`: Timestamp tạo response

## Cách sử dụng

### 1. Lấy vụ việc mới nhất cho dashboard
```javascript
const response = await fetch('/api/v1.0/statistics/latest-incidents', {
  method: 'POST',
  headers: {
    'token': 'your-auth-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({})
});

const data = await response.json();
console.log('Số vụ việc:', data.data.total);
console.log('Danh sách vụ việc:', data.data.incidents);
```

### 2. Lấy vụ việc theo khu vực cụ thể
```javascript
const response = await fetch('/api/v1.0/statistics/latest-incidents', {
  method: 'POST',
  headers: {
    'token': 'your-auth-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    area: '507f1f77bcf86cd799439011'
  })
});

const data = await response.json();
console.log('Vụ việc trong khu vực:', data.data.incidents);
```

## Lưu ý quan trọng

1. **Giới hạn kết quả**: API luôn trả về tối đa 3 vụ việc mới nhất
2. **Nguồn dữ liệu**: Lấy vụ việc từ trường `metrics` của báo cáo, không phải từ báo cáo trực tiếp
3. **Filter theo chartType**: Chỉ lấy các báo cáo có `jobType` với `chartTypes` chứa "highlight"
4. **Filter theo metric**: Chỉ lấy các metric có `needsDetails = true` trong JobType configuration
5. **Logic ưu tiên**: Lấy từ báo cáo mới nhất trước, nếu không đủ 3 vụ việc thì lấy thêm từ báo cáo tiếp theo
6. **Sắp xếp**: Báo cáo được sắp xếp theo `createdAt` giảm dần (mới nhất trước)
7. **Trạng thái báo cáo**: Không lấy các báo cáo có status là "draft"
8. **Filter khu vực**: Khi có tham số `area`, sẽ filter theo cả `details.location.areas` và `createdBy.areas`
9. **ID vụ việc**: Mỗi vụ việc có ID duy nhất theo format `reportId_metricKey_incidentIndex`

## Test API

Sử dụng file `test_latest_incidents_api.js` để test API:

```bash
node test_latest_incidents_api.js
```

Nhớ cập nhật `TEST_TOKEN` và `API_BASE_URL` trong file test trước khi chạy.
