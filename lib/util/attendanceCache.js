/**
 * Redis caching utility cho hệ thống điểm danh
 * Tối ưu hóa performance cho các truy vấn thường xuyên
 */

const redisConnection = require('../connections/redis');

class AttendanceCache {
  constructor() {
    this.redis = redisConnection('master').getConnection();
    this.TTL = {
      USER_SCHEDULE: 3600, // 1 hour
      ATTENDANCE_STATS: 1800, // 30 minutes
      NOTIFICATION_SCHEDULE: 600, // 10 minutes
      PERMISSION_CHECK: 300 // 5 minutes
    };
  }

  /**
   * Tạo cache key
   * @param {String} prefix - Prefix của key
   * @param {Array} params - Tham số để tạo key
   * @returns {String} Cache key
   */
  generateKey(prefix, ...params) {
    return `attendance:${prefix}:${params.join(':')}`;
  }

  /**
   * Cache lịch làm việc của user
   * @param {String} userId - ID user
   * @param {String} startDate - <PERSON><PERSON><PERSON> bắ<PERSON> đầ<PERSON>
   * @param {String} endDate - <PERSON><PERSON><PERSON> kế<PERSON> thú<PERSON>
   * @param {Array} schedules - <PERSON><PERSON> liệ<PERSON> lịch làm việc
   */
  async cacheUserSchedule(userId, startDate, endDate, schedules) {
    try {
      const key = this.generateKey('user_schedule', userId, startDate, endDate);
      await this.redis.setex(key, this.TTL.USER_SCHEDULE, JSON.stringify(schedules));
    } catch (error) {
      console.error('Error caching user schedule:', error);
    }
  }

  /**
   * Lấy lịch làm việc từ cache
   * @param {String} userId - ID user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Array|null} Dữ liệu lịch làm việc hoặc null
   */
  async getUserSchedule(userId, startDate, endDate) {
    try {
      const key = this.generateKey('user_schedule', userId, startDate, endDate);
      this.redis.get(key, (err, cached) => {
        if (err) {
          console.error('Error getting user schedule from cache:', err);
          return null;
        }
        return cached ? JSON.parse(cached) : null;
      });
    } catch (error) {
      console.error('Error getting user schedule from cache:', error);
      return null;
    }
  }

  /**
   * Xóa cache lịch làm việc của user
   * @param {String} userId - ID user
   */
  async invalidateUserSchedule(userId) {
    try {
      const pattern = this.generateKey('user_schedule', userId, '*');
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.error('Error invalidating user schedule cache:', error);
    }
  }

  /**
   * Cache thống kê điểm danh
   * @param {String} userId - ID user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {Object} statistics - Dữ liệu thống kê
   */
  async cacheAttendanceStats(userId, startDate, endDate, statistics) {
    try {
      const key = this.generateKey('attendance_stats', userId, startDate, endDate);
      await this.redis.setex(key, this.TTL.ATTENDANCE_STATS, JSON.stringify(statistics));
    } catch (error) {
      console.error('Error caching attendance stats:', error);
    }
  }

  /**
   * Lấy thống kê điểm danh từ cache
   * @param {String} userId - ID user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object|null} Dữ liệu thống kê hoặc null
   */
  async getAttendanceStats(userId, startDate, endDate) {
    try {
      const key = this.generateKey('attendance_stats', userId, startDate, endDate);
      this.redis.get(key, (err, cached) => {
        if (err) {
          console.error('Error getting attendance stats from cache:', err);
          return null;
        }
        return cached ? JSON.parse(cached) : null;
      });
    } catch (error) {
      console.error('Error getting attendance stats from cache:', error);
      return null;
    }
  }

  /**
   * Xóa cache thống kê điểm danh
   * @param {String} userId - ID user (optional)
   */
  async invalidateAttendanceStats(userId = '*') {
    try {
      const pattern = this.generateKey('attendance_stats', userId, '*');
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.error('Error invalidating attendance stats cache:', error);
    }
  }

  /**
   * Cache kết quả kiểm tra quyền
   * @param {String} userId - ID user
   * @param {String} action - Hành động
   * @param {String} targetId - ID đối tượng
   * @param {Object} result - Kết quả kiểm tra quyền
   */
  async cachePermissionCheck(userId, action, targetId, result) {
    try {
      const key = this.generateKey('permission', userId, action, targetId);
      await this.redis.setex(key, this.TTL.PERMISSION_CHECK, JSON.stringify(result));
    } catch (error) {
      console.error('Error caching permission check:', error);
    }
  }

  /**
   * Lấy kết quả kiểm tra quyền từ cache
   * @param {String} userId - ID user
   * @param {String} action - Hành động
   * @param {String} targetId - ID đối tượng
   * @returns {Object|null} Kết quả kiểm tra quyền hoặc null
   */
  async getPermissionCheck(userId, action, targetId) {
    try {
      const key = this.generateKey('permission', userId, action, targetId);
      this.redis.get(key, (err, cached) => {
        if (err) {
          console.error('Error getting permission check from cache:', err);
          return null;
        }
        return cached ? JSON.parse(cached) : null;
      });
    } catch (error) {
      console.error('Error getting permission check from cache:', error);
      return null;
    }
  }

  /**
   * Cache danh sách user được quản lý
   * @param {String} userId - ID user
   * @param {Array} managedUsers - Danh sách user được quản lý
   */
  async cacheManagedUsers(userId, managedUsers) {
    try {
      const key = this.generateKey('managed_users', userId);
      await this.redis.setex(key, this.TTL.PERMISSION_CHECK, JSON.stringify(managedUsers));
    } catch (error) {
      console.error('Error caching managed users:', error);
    }
  }

  /**
   * Lấy danh sách user được quản lý từ cache
   * @param {String} userId - ID user
   * @returns {Array|null} Danh sách user hoặc null
   */
  async getManagedUsers(userId) {
    try {
      const key = this.generateKey('managed_users', userId);
      this.redis.get(key, (err, cached) => {
        if (err) {
          console.error('Error getting managed users from cache:', err);
          return null;
        }
        return cached ? JSON.parse(cached) : null;
      });
    } catch (error) {
      console.error('Error getting managed users from cache:', error);
      return null;
    }
  }

  /**
   * Cache trạng thái điểm danh hôm nay
   * @param {String} userId - ID user
   * @param {String} date - Ngày
   * @param {Object} status - Trạng thái điểm danh
   */
  async cacheTodayStatus(userId, date, status) {
    try {
      const key = this.generateKey('today_status', userId, date);
      await this.redis.setex(key, 3600, JSON.stringify(status)); // Cache 1 hour
    } catch (error) {
      console.error('Error caching today status:', error);
    }
  }

  /**
   * Lấy trạng thái điểm danh hôm nay từ cache
   * @param {String} userId - ID user
   * @param {String} date - Ngày
   * @returns {Object|null} Trạng thái điểm danh hoặc null
   */
  async getTodayStatus(userId, date) {
    try {
      const key = this.generateKey('today_status', userId, date);
      this.redis.get(key, (err, cached) => {
        if (err) {
          console.error('Error getting today status from cache:', err);
          return null;
        }
        return cached ? JSON.parse(cached) : null;
      });
    } catch (error) {
      console.error('Error getting today status from cache:', error);
      return null;
    }
  }

  /**
   * Xóa cache trạng thái hôm nay khi có điểm danh mới
   * @param {String} userId - ID user
   * @param {String} date - Ngày
   */
  async invalidateTodayStatus(userId, date) {
    try {
      const key = this.generateKey('today_status', userId, date);
      await this.redis.del(key);
    } catch (error) {
      console.error('Error invalidating today status cache:', error);
    }
  }

  /**
   * Xóa tất cả cache liên quan đến user
   * @param {String} userId - ID user
   */
  async invalidateUserCache(userId) {
    try {
      const patterns = [
        this.generateKey('user_schedule', userId, '*'),
        this.generateKey('attendance_stats', userId, '*'),
        this.generateKey('permission', userId, '*'),
        this.generateKey('managed_users', userId),
        this.generateKey('today_status', userId, '*')
      ];

      for (const pattern of patterns) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
    } catch (error) {
      console.error('Error invalidating user cache:', error);
    }
  }

  /**
   * Lấy thống kê cache
   * @returns {Object} Thống kê cache
   */
  async getCacheStats() {
    try {
      const info = await this.redis.info('memory');
      const keyCount = await this.redis.dbsize();

      return {
        keyCount,
        memoryInfo: info
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return null;
    }
  }

  /**
   * Xóa tất cả cache của attendance system
   */
  async clearAllCache() {
    try {
      const pattern = 'attendance:*';
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
      return keys.length;
    } catch (error) {
      console.error('Error clearing all cache:', error);
      return 0;
    }
  }
}

module.exports = new AttendanceCache();