const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const axios = require('axios');
const rp = require('request-promise');
const PushNotifyManager = require('../jobs/pushNotify');
const MeetingSchedule = require('../models/meetingSchedule');
const UserModel = require('../models/user');

class MeetingManager {
  constructor() {
    this.reminderIntervals = [
      { time: 60 * 60 * 1000, label: '1 giờ' },       // 1 hour before
      { time: 15 * 60 * 1000, label: '15 phút' }      // 15 minutes before
    ];
  }

  /**
   * G<PERSON>i thông báo nhắc lịch họp cho cán bộ
   * @param {String} meetingId - ID của cuộc họp
   * @param {Number} reminderTime - Thời gian nh<PERSON>c trước (milliseconds)
   */
  async sendMeetingReminder(meetingId, reminderTime) {
    try {
      const meeting = await MeetingSchedule.findById(meetingId)
        .populate('officers', 'name')
        .populate('assignedBy', 'name');

      if (!meeting || meeting.status !== 1) {
        console.log(`Meeting ${meetingId} not found or inactive`);
        return;
      }

      const now = Date.now();
      const timeUntilMeeting = meeting.startTime - now;
      
      // Kiểm tra xem có phải thời điểm nhắc nhở không
      if (Math.abs(timeUntilMeeting - reminderTime) > 5 * 60 * 1000) { // tolerance 5 minutes
        return;
      }

      // Xác định loại thông báo
      const reminderType = reminderTime === 60 * 60 * 1000 ? '1hour' : '15minutes';
      
      // Kiểm tra xem đã gửi thông báo loại này chưa
      const alreadySent = meeting.sentReminders && meeting.sentReminders.some(
        reminder => reminder.type === reminderType
      );
      
      if (alreadySent) {
        console.log(`${reminderType} reminder already sent for meeting ${meetingId}`);
        return;
      }

      const meetingTimeFormatted = moment(meeting.startTime).format('HH:mm');
      
      // Tính thời gian chính xác còn lại đến cuộc họp
      const minutesUntilMeeting = Math.round(timeUntilMeeting / (60 * 1000));
      const timeLabel = minutesUntilMeeting > 60 
        ? `${Math.round(minutesUntilMeeting / 60)} giờ ${minutesUntilMeeting % 60} phút`
        : `${minutesUntilMeeting} phút`;
      
      const title = `Nhắc nhở Lịch họp`;
      const description = `${timeLabel} nữa Cán bộ có Cuộc họp "${meeting.topic}" bắt đầu vào ${meetingTimeFormatted}. Ấn để xem chi tiết lịch họp.`;
      
      const notificationData = {
        link: 'ScheduleScreen',
        extras: {
          _id: meeting._id
        }
      };

      // Gửi thông báo cho từng cán bộ
      for (const officer of meeting.officers) {
        try {
          await PushNotifyManager.sendToMember(
            officer._id,
            title,
            description,
            notificationData,
            '',
            'ioc'
          );
          
          console.log(`Sent meeting reminder to ${officer.fullName} for meeting: ${meeting.topic}`);
        } catch (error) {
          console.error(`Failed to send reminder to ${officer.fullName}:`, error);
        }
      }

      // Lưu lại thông tin thông báo đã gửi
      await MeetingSchedule.findByIdAndUpdate(meetingId, {
        $push: {
          sentReminders: {
            type: reminderType,
            sentAt: now
          }
        }
      });
      
      console.log(`Saved ${reminderType} reminder record for meeting ${meetingId}`);

    } catch (error) {
      console.error('Error sending meeting reminder:', error);
    }
  }

  /**
   * Tìm và gửi tất cả thông báo nhắc lịch họp cần thiết
   */
  async processUpcomingMeetings() {
    try {
      const now = Date.now();
      
      // Tìm các cuộc họp sắp tới trong 2 giờ tới
      const upcomingMeetings = await MeetingSchedule.find({
        startTime: {
          $gte: now,
          $lte: now + (2 * 60 * 60 * 1000) // 2 hours from now
        },
        status: 1
      }).populate('officers', 'name');

      console.log(`Found ${upcomingMeetings.length} upcoming meetings`);

      for (const meeting of upcomingMeetings) {
        for (const interval of this.reminderIntervals) {
          const reminderTime = meeting.startTime - interval.time;
          // Nếu thời gian nhắc nhở đã qua hoặc sắp tới (trong vòng 5 phút)
          if (reminderTime <= now + (5 * 60 * 1000) && reminderTime >= now - (5 * 60 * 1000)) {
            await this.sendMeetingReminder(meeting._id, interval.time);
          }
        }
      }

    } catch (error) {   
      console.error('Error processing upcoming meetings:', error);
    }
  }

  /**
   * Lấy label mô tả thời gian nhắc nhở
   * @param {Number} reminderTime - Thời gian nhắc (milliseconds)
   */
  getReminderLabel(reminderTime) {
    const interval = this.reminderIntervals.find(i => i.time === reminderTime);
    console.log(`Getting label for reminder time: ${reminderTime}ms`);
    return interval ? interval.label : `${Math.floor(reminderTime / (60 * 1000))} phút`;
  }

  /**
   * Khởi tạo job định kỳ để kiểm tra và gửi thông báo
   */
  startReminderJob() {
    // Chạy mỗi 5 phút để kiểm tra lịch họp
    this.processUpcomingMeetings();
    setInterval(() => {
      this.processUpcomingMeetings();
    }, 5 * 60 * 1000); // 5 minutes

    console.log('Meeting reminder job started - checking every 5 minutes');
  }
}

module.exports = new MeetingManager;
