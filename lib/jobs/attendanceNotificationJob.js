/**
 * Background job xử lý thông báo điểm danh định kỳ
 * Chạy mỗi phút để kiểm tra và gửi thông báo
 */

const notificationService = require('../services/notificationService');

class AttendanceNotificationJob {
  /**
   * Chạy job xử lý thông báo
   */
  async run() {
    try {
      console.log(`[${new Date().toISOString()}] Starting attendance notification job...`);

      const result = await notificationService.processScheduledNotifications();

      if (result.success) {
        console.log(`[${new Date().toISOString()}] Notification job completed: ${result.message}`);
      } else {
        console.error(`[${new Date().toISOString()}] Notification job failed: ${result.message}`);
      }

      return result;

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Notification job error:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Khởi động job với interval
   * @param {Number} intervalMinutes - <PERSON><PERSON><PERSON><PERSON> thời gian ch<PERSON> (phút)
   */
  start(intervalMinutes = 1) {
    console.log(`[${new Date().toISOString()}] Starting attendance notification job with ${intervalMinutes} minute interval`);

    // Chạy ngay lần đầu
    this.run();

    // Lên lịch chạy định kỳ
    this.interval = setInterval(() => {
      this.run();
    }, intervalMinutes * 60 * 1000);

    return this.interval;
  }

  /**
   * Dừng job
   */
  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      console.log(`[${new Date().toISOString()}] Attendance notification job stopped`);
    }
  }
}

module.exports = new AttendanceNotificationJob();