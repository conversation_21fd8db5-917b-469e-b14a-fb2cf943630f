/**
 * Job xử lý tự động cho hệ thống chấm công đột xuất
 * - Cập nhật trạng thái phiên chấm công
 * - G<PERSON>i thông báo nhắc nhở
 * - Cập nhật trạng thái vắng mặt
 */

const cron = require('node-cron');
const SuddenAttendanceSession = require('../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../models/suddenAttendanceRecord');
const NotificationHelper = require('../util/notificationHelper');
const CONSTANTS = require('../const');

class SuddenAttendanceJob {
  constructor() {
    this.isRunning = false;
  }

  /**
   * Khởi tạo các cron jobs
   */
  init() {
    console.log('[SuddenAttendanceJob] Initializing sudden attendance jobs...');

    // Chạy mỗi phút để kiểm tra và cập nhật trạng thái
    cron.schedule('* * * * *', () => {
      this.processSessionUpdates();
    });

    // Chạy mỗi 5 phút để gửi thông báo nhắc nhở
    cron.schedule('*/5 * * * *', () => {
      this.sendReminders();
    });

    console.log('[SuddenAttendanceJob] Sudden attendance jobs initialized');
  }

  /**
   * Xử lý cập nhật trạng thái phiên chấm công
   */
  async processSessionUpdates() {
    if (this.isRunning) return;
    
    try {
      this.isRunning = true;
      const now = Date.now();

      // 1. Kích hoạt các phiên đã đến giờ bắt đầu
      await this.activateSessions(now);

      // 2. Hoàn thành các phiên đã hết thời gian chấm công muộn
      await this.completeSessions(now);

      // 3. Cập nhật trạng thái vắng mặt cho những người chưa chấm công
      await this.updateAbsentStatus(now);

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error in processSessionUpdates:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Kích hoạt các phiên chấm công đã đến giờ bắt đầu
   */
  async activateSessions(now) {
    try {
      const sessionsToActivate = await SuddenAttendanceSession.find({
        status: 'scheduled',
        startTime: { $lte: now }
      });

      for (const session of sessionsToActivate) {
        await SuddenAttendanceSession.findByIdAndUpdate(session._id, {
          status: 'active'
        });

        console.log(`[SuddenAttendanceJob] Activated session: ${session.title} (${session._id})`);
      }

      if (sessionsToActivate.length > 0) {
        console.log(`[SuddenAttendanceJob] Activated ${sessionsToActivate.length} sessions`);
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error activating sessions:', error);
    }
  }

  /**
   * Hoàn thành các phiên đã hết thời gian chấm công
   */
  async completeSessions(now) {
    try {
      const sessionsToComplete = await SuddenAttendanceSession.find({
        status: 'active',
        startTime: { $lte: now - (60 * 60 * 1000) } // 1 giờ sau giờ bắt đầu
      });

      for (const session of sessionsToComplete) {
        await SuddenAttendanceSession.findByIdAndUpdate(session._id, {
          status: 'completed'
        });

        console.log(`[SuddenAttendanceJob] Completed session: ${session.title} (${session._id})`);
      }

      if (sessionsToComplete.length > 0) {
        console.log(`[SuddenAttendanceJob] Completed ${sessionsToComplete.length} sessions`);
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error completing sessions:', error);
    }
  }

  /**
   * Cập nhật trạng thái vắng mặt cho những người chưa chấm công
   */
  async updateAbsentStatus(now) {
    try {
      // Tìm các phiên đã hết thời gian chấm công (1 giờ sau giờ bắt đầu)
      const expiredSessions = await SuddenAttendanceSession.find({
        status: { $in: ['active', 'completed'] },
        startTime: { $lte: now - (60 * 60 * 1000) }
      });

      for (const session of expiredSessions) {
        // Lấy danh sách người đã chấm công
        const checkedInUsers = await SuddenAttendanceRecord.find({
          session: session._id
        }).distinct('user');

        // Tìm những người chưa chấm công
        const absentUsers = session.targetUsers.filter(
          userId => !checkedInUsers.some(checkedUserId => 
            checkedUserId.toString() === userId.toString()
          )
        );

        // Tạo bản ghi vắng mặt cho những người chưa chấm công
        for (const userId of absentUsers) {
          const existingRecord = await SuddenAttendanceRecord.findOne({
            session: session._id,
            user: userId
          });

          if (!existingRecord) {
            await SuddenAttendanceRecord.create({
              session: session._id,
              user: userId,
              checkinTime: session.startTime + (60 * 60 * 1000), // Thời gian hết hạn
              status: CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.ABSENT,
              note: 'Tự động cập nhật vắng mặt do không chấm công trong thời gian quy định'
            });
          }
        }

        // Cập nhật thống kê phiên
        if (absentUsers.length > 0) {
          await SuddenAttendanceSession.findByIdAndUpdate(session._id, {
            $inc: { 'statistics.absentCount': absentUsers.length }
          });

          console.log(`[SuddenAttendanceJob] Updated ${absentUsers.length} absent records for session: ${session.title}`);
        }
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error updating absent status:', error);
    }
  }

  /**
   * Gửi thông báo nhắc nhở chấm công
   */
  async sendReminders() {
    try {
      const now = Date.now();

      // Tìm các phiên đang active và sắp hết thời gian chấm công đúng giờ
      const activeSessions = await SuddenAttendanceSession.find({
        status: 'active',
        startTime: { 
          $gte: now - (30 * 60 * 1000), // Bắt đầu từ 30 phút trước
          $lte: now 
        }
      });

      for (const session of activeSessions) {
        const validEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
        const timeUntilEnd = validEndTime - now;

        // Gửi nhắc nhở khi còn 5 phút hoặc 1 phút
        if (timeUntilEnd > 0 && (
          Math.abs(timeUntilEnd - 5 * 60 * 1000) < 30 * 1000 || // Còn 5 phút (±30s)
          Math.abs(timeUntilEnd - 1 * 60 * 1000) < 30 * 1000    // Còn 1 phút (±30s)
        )) {
          // Lấy danh sách người chưa chấm công
          const checkedInUsers = await SuddenAttendanceRecord.find({
            session: session._id
          }).distinct('user');

          const uncheckedUsers = session.targetUsers.filter(
            userId => !checkedInUsers.some(checkedUserId => 
              checkedUserId.toString() === userId.toString()
            )
          );

          if (uncheckedUsers.length > 0) {
            await NotificationHelper.notifyMissedSuddenCheckin(uncheckedUsers, {
              _id: session._id,
              title: session.title,
              startTime: session.startTime,
              validDurationMinutes: session.validDurationMinutes
            });

            console.log(`[SuddenAttendanceJob] Sent reminders to ${uncheckedUsers.length} users for session: ${session.title}`);
          }
        }
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error sending reminders:', error);
    }
  }

  /**
   * Dừng tất cả jobs
   */
  stop() {
    console.log('[SuddenAttendanceJob] Stopping sudden attendance jobs...');
    // Cron jobs sẽ tự động dừng khi process kết thúc
  }
}

module.exports = new SuddenAttendanceJob();
