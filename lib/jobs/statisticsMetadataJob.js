const cron = require('node-cron');
const statisticsMetadataService = require('../services/statisticsMetadataService');

/**
 * Background job xử lý metadata thống kê định kỳ
 * - Tính toán metadata mỗi 10 phút
 * - Xử lý trigger events mỗi phút
 * - Gửi real-time notifications khi có thay đổi
 */
class StatisticsMetadataJob {
  constructor() {
    this.metadataInterval = null;
    this.triggerInterval = null;
  }

  /**
   * Khởi động tất cả jobs
   */
  start() {
    this.startMetadataCalculation();
    this.startTriggerEventProcessing();
    console.log(`[${new Date().toISOString()}] Statistics metadata jobs started`);
  }

  /**
   * Khởi động job tính toán metadata định kỳ
   * Chạy mỗi 10 phút
   */
  startMetadataCalculation() {
    // Chạy ngay lần đầu
    this.calculateMetadata();

    // Lên lịch chạy mỗi 10 phút
    cron.schedule('*/10 * * * *', () => {
      this.calculateMetadata();
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    console.log(`[${new Date().toISOString()}] Metadata calculation job scheduled - every 10 minutes`);
  }

  /**
   * Khởi động job xử lý trigger events
   * Chạy mỗi phút
   */
  startTriggerEventProcessing() {
    // Chạy ngay lần đầu
    this.processTriggerEvents();

    // Lên lịch chạy mỗi phút
    cron.schedule('* * * * *', () => {
      this.processTriggerEvents();
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    console.log(`[${new Date().toISOString()}] Trigger event processing job scheduled - every minute`);
  }

  /**
   * Thực hiện tính toán metadata
   */
  async calculateMetadata() {
    try {
      console.log(`[${new Date().toISOString()}] Starting metadata calculation...`);
      
      const result = await statisticsMetadataService.calculateAllMetadata();
      
      if (result.success) {
        console.log(`[${new Date().toISOString()}] Metadata calculation completed successfully`);
      } else {
        console.error(`[${new Date().toISOString()}] Metadata calculation failed:`, result.error);
      }

      // Log kết quả
      if (global.logger) {
        global.logger.logInfo('[STATISTICS_METADATA_JOB]', result);
      }

      return result;

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Metadata calculation error:`, error);
      
      if (global.logger) {
        global.logger.logError(['Statistics metadata calculation error:', error]);
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Xử lý trigger events
   */
  async processTriggerEvents() {
    try {
      const result = await statisticsMetadataService.processTriggeredEvents();
      
      if (result.processed > 0) {
        console.log(`[${new Date().toISOString()}] Processed ${result.processed} trigger events. Affected: ${result.affectedTypes?.join(', ') || 'none'}`);
      }

      return result;

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Trigger event processing error:`, error);
      
      if (global.logger) {
        global.logger.logError(['Statistics trigger event processing error:', error]);
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Dừng tất cả jobs
   */
  stop() {
    if (this.metadataInterval) {
      clearInterval(this.metadataInterval);
      this.metadataInterval = null;
    }
    
    if (this.triggerInterval) {
      clearInterval(this.triggerInterval);
      this.triggerInterval = null;
    }
    
    console.log(`[${new Date().toISOString()}] Statistics metadata jobs stopped`);
  }

  /**
   * Chạy metadata calculation ngay lập tức (để test)
   */
  async runMetadataCalculationNow() {
    console.log(`[${new Date().toISOString()}] Running metadata calculation manually...`);
    
    try {
      const result = await this.calculateMetadata();
      console.log('Manual metadata calculation result:', result);
      return result;
    } catch (error) {
      console.error('Manual metadata calculation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Chạy trigger event processing ngay lập tức (để test)
   */
  async runTriggerProcessingNow() {
    console.log(`[${new Date().toISOString()}] Running trigger event processing manually...`);
    
    try {
      const result = await this.processTriggerEvents();
      console.log('Manual trigger processing result:', result);
      return result;
    } catch (error) {
      console.error('Manual trigger processing error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Trigger một event cụ thể (để test hoặc gọi từ các API khác)
   * @param {String} eventType - Loại event
   * @param {Object} eventData - Dữ liệu event
   */
  triggerEvent(eventType, eventData = {}) {
    try {
      statisticsMetadataService.registerTriggerEvent(eventType, eventData);
      console.log(`[${new Date().toISOString()}] Triggered event: ${eventType}`);
      
      return { success: true, eventType, eventData };
      
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error triggering event ${eventType}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Invalidate cache cho một loại thống kê cụ thể
   * @param {String} type - Loại thống kê
   * @param {String} timeRange - Khoảng thời gian (optional)
   */
  async invalidateCache(type, timeRange = null) {
    try {
      await statisticsMetadataService.invalidateCache(type, timeRange);
      console.log(`[${new Date().toISOString()}] Cache invalidated for ${type}`);
      
      return { success: true, type, timeRange };
      
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error invalidating cache for ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Lấy thống kê về job performance
   */
  getJobStats() {
    return {
      metadataJobActive: !!this.metadataInterval,
      triggerJobActive: !!this.triggerInterval,
      lastMetadataRun: this.lastMetadataRun || null,
      lastTriggerRun: this.lastTriggerRun || null,
      uptime: process.uptime()
    };
  }
}

module.exports = new StatisticsMetadataJob();
