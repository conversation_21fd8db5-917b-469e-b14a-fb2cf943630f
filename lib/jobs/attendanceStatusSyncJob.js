/**
 * Background job đồng bộ trạng thái attendance
 * <PERSON><PERSON><PERSON> định kỳ để cập nhật trạng thái missed/absent cho các ca đã hết giờ
 */

const cron = require('node-cron');
const attendanceStatusSyncService = require('../services/attendanceStatusSyncService');
const DateUtils = require('../utils/dateUtils');

class AttendanceStatusSyncJob {
  constructor() {
    this.isRunning = false;
    this.cronJob = null;
  }

  /**
   * Khởi động job với cron schedule
   * Mặc định chạy vào 12:00 và 18:00 hàng ngày (sau khi ca làm việc kết thúc)
   */
  start(cronSchedule = '0 12,18 * * *') {
    if (this.cronJob) {
      console.log('[ATTENDANCE_SYNC_JOB] Job already running, stopping previous job');
      this.stop();
    }

    console.log(`[ATTENDANCE_SYNC_JOB] Starting with schedule: ${cronSchedule}`);

    this.cronJob = cron.schedule(cronSchedule, async () => {
      await this.run();
    }, {
      timezone: "Asia/Ho_Chi_Minh",
      scheduled: true
    });

    console.log('[ATTENDANCE_SYNC_JOB] Job scheduled successfully');
  }

  /**
   * Dừng job
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      console.log('[ATTENDANCE_SYNC_JOB] Job stopped');
    }
  }

  /**
   * Chạy job ngay lập tức
   */
  async run() {
    if (this.isRunning) {
      console.log('[ATTENDANCE_SYNC_JOB] Job is already running, skipping...');
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      console.log(`[ATTENDANCE_SYNC_JOB] Starting attendance status sync at ${new Date().toISOString()}`);

      // Sync cho ngày hiện tại (chạy sau khi ca làm việc kết thúc)
      const today = new Date();
      const targetDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(today.toISOString().split('T')[0]);

      const result = await attendanceStatusSyncService.syncMissedAbsentStatus(targetDate);

      const duration = Date.now() - startTime;

      if (result.success) {
        console.log(`[ATTENDANCE_SYNC_JOB] Completed successfully in ${duration}ms:`, result.data);
      } else {
        console.error(`[ATTENDANCE_SYNC_JOB] Failed after ${duration}ms:`, result.message);
      }

      // Log kết quả nếu có global logger
      if (global.logger) {
        global.logger.logInfo('[ATTENDANCE_SYNC_JOB]', {
          duration,
          result
        });
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[ATTENDANCE_SYNC_JOB] Error after ${duration}ms:`, error);

      if (global.logger) {
        global.logger.logError('[ATTENDANCE_SYNC_JOB]', error);
      }
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Chạy sync cho khoảng thời gian cụ thể (manual)
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Object} Kết quả sync
   */
  async runForDateRange(startDate, endDate) {
    if (this.isRunning) {
      return {
        success: false,
        message: 'Job is already running, please wait...',
        data: null
      };
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      console.log(`[ATTENDANCE_SYNC_JOB] Manual sync for date range ${startDate} to ${endDate}`);

      const result = await attendanceStatusSyncService.batchSyncMissedAbsentStatus(startDate, endDate);

      const duration = Date.now() - startTime;
      console.log(`[ATTENDANCE_SYNC_JOB] Manual sync completed in ${duration}ms`);

      return result;

    } catch (error) {
      console.error('[ATTENDANCE_SYNC_JOB] Error in manual sync:', error);
      return {
        success: false,
        message: error.message,
        data: null
      };
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Lấy trạng thái job
   * @returns {Object} Trạng thái hiện tại
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isScheduled: !!this.cronJob,
      nextRun: this.cronJob ? 'Scheduled for 12:00 and 18:00 daily' : 'Not scheduled'
    };
  }

  /**
   * Chạy sync cho ngày hôm qua (tiện ích)
   * @returns {Object} Kết quả sync
   */
  async syncYesterday() {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const targetDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(yesterday.toISOString().split('T')[0]);

    return await attendanceStatusSyncService.syncMissedAbsentStatus(targetDate);
  }

  /**
   * Chạy sync cho ngày hôm nay (chỉ dùng khi debug)
   * @returns {Object} Kết quả sync
   */
  async syncToday() {
    const today = DateUtils.getCurrentDateDDMMYYYY();
    console.warn('[ATTENDANCE_SYNC_JOB] WARNING: Syncing today - some shifts may not be completed yet');

    return await attendanceStatusSyncService.syncMissedAbsentStatus(today);
  }
}

module.exports = new AttendanceStatusSyncJob();
