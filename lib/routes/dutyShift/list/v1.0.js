const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { weekType, startTime, endTime } = req.body;
  const currentDate = new Date();
  let targetDate;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    
    // Validate weekType parameter nếu có
    if (weekType) {
      const validWeekTypes = ['previous', 'current', 'next'];
      if (!validWeekTypes.includes(weekType)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'weekType phải là previous, current hoặc next'
          }
        });
      }
    }
    
    // Validate startTime và endTime nếu có
    if (startTime || endTime) {
      if (startTime && !_.isNumber(startTime)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian bắt đầu phải là Số (number)'
          }
        });
      }
      
      if (endTime && !_.isNumber(endTime)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian kết thúc phải là Số (number)'
          }
        });
      }
      
      if (startTime && endTime && startTime >= endTime) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
          }
        });
      }
      
      // Kiểm tra khoảng thời gian tối đa 31 ngày
      if (startTime && endTime) {
        const maxDuration = 31 * 24 * 60 * 60 * 1000; // 31 ngày tính bằng milliseconds
        const duration = endTime - startTime;
        
        if (duration > maxDuration) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Khoảng thời gian tối đa là 31 ngày'
            }
          });
        }
      }
    }
    
    next();
  };

  const getDutyShifts = (next) => {
    let query = {};
    let dateRange = {};
    let isUsingTimeRange = false;

    // Ưu tiên sử dụng startTime và endTime nếu có
    if (startTime || endTime) {
      isUsingTimeRange = true;
      
      if (startTime) {
        query.startTime = { $gte: startTime };
      }
      
      if (endTime) {
        query.endTime = { $lte: endTime };
      }
      
      dateRange = {
        startOfWeek: startTime || null,
        endOfWeek: endTime || null,
        weekRange: startTime && endTime ? 
          `${new Date(startTime).toLocaleDateString('vi-VN')} - ${new Date(endTime).toLocaleDateString('vi-VN')}` : 
          'Custom range'
      };
    }
    // Nếu không có startTime/endTime, sử dụng weekType
    else if (weekType) {
      isUsingTimeRange = true;
      targetDate = new Date(currentDate);
      
      if (weekType === 'previous') {
        targetDate.setDate(currentDate.getDate() - 7);
      } else if (weekType === 'next') {
        targetDate.setDate(currentDate.getDate() + 7);
      }

      // Tính toán startTime và endTime của tuần (Thứ 2 đến Chủ nhật)
      const startOfWeek = (() => {
        const date = new Date(targetDate);
        const day = date.getDay();
        // Tính số ngày cần lùi về thứ 2 (day = 1)
        const diff = day === 0 ? -6 : 1 - day; // Nếu là Chủ nhật (0) thì lùi 6 ngày, ngược lại lùi (1-day) ngày
        const mondayDate = new Date(date);
        mondayDate.setDate(mondayDate.getDate() + diff);
        mondayDate.setHours(0, 0, 0, 0);
        return mondayDate.getTime();
      })();

      const endOfWeek = (() => {
        const date = new Date(targetDate);
        const day = date.getDay();
        // Tính số ngày cần tiến tới Chủ nhật (day = 0)
        // Nếu là Chủ nhật thì tiến 0 ngày, ngược lại tiến (7-day) ngày để tới chủ nhật cùng tuần
        const diff = day === 0 ? 0 : 7 - day;
        const sundayDate = new Date(date);
        sundayDate.setDate(sundayDate.getDate() + diff);
        sundayDate.setHours(23, 59, 59, 999);
        return sundayDate.getTime();
      })();

      dateRange = {
        startOfWeek,
        endOfWeek,
        weekRange: `${new Date(startOfWeek).toLocaleDateString('vi-VN')} - ${new Date(endOfWeek).toLocaleDateString('vi-VN')}`
      };

      query.startTime = { $gte: startOfWeek };
      query.endTime = { $lte: endOfWeek };
    }
    
    query.status = 1;
    // Lấy danh sách ca trực (không phân trang)
    DutyShiftModel.find(query)
      .populate('officer', 'name email phone')
      .populate('unit', 'name')
      .populate('assignedBy', 'name')
      .sort({ startTime: 1 }) // Sắp xếp theo thời gian bắt đầu
      .lean()
      .exec((err, shifts) => {
        if (err) {
          return next(err);
        }

        // Nhóm ca trực theo ngày nếu có khoảng thời gian
        let groupedShifts = null;
        if (isUsingTimeRange) {
          const shiftsByDate = {};
          shifts.forEach(shift => {
            const shiftDate = new Date(shift.startTime);
            // Tạo dateKey dựa trên ngày/tháng/năm để đảm bảo cùng ngày được group lại
            const year = shiftDate.getFullYear();
            const month = String(shiftDate.getMonth() + 1).padStart(2, '0');
            const day = String(shiftDate.getDate()).padStart(2, '0');
            const dateKey = `${year}-${month}-${day}`;
            
            if (!shiftsByDate[dateKey]) {
              shiftsByDate[dateKey] = [];
            }
            
            shiftsByDate[dateKey].push(shift);
          });

          // Chuyển đổi sang format mong muốn
          groupedShifts = Object.keys(shiftsByDate)
            .sort() // Sắp xếp theo ngày
            .map(dateKey => {
              // Sử dụng shift đầu tiên để lấy thông tin ngày chính xác
              const firstShift = shiftsByDate[dateKey][0];
              const shiftDate = new Date(firstShift.startTime);
              const dayOfWeek = shiftDate.getDay();
              
              // Format ngày theo dd/MM
              const day = String(shiftDate.getDate()).padStart(2, '0');
              const month = String(shiftDate.getMonth() + 1).padStart(2, '0');
              
              return {
                date: `${day}/${month}`,
                dayOfWeek: getDayName(dayOfWeek),
                shifts: shiftsByDate[dateKey]
              };
            });
        }

        const result = {
          data: isUsingTimeRange ? groupedShifts : shifts,
          total: shifts.length
        };

        // Thêm thông tin thời gian nếu có khoảng thời gian
        if (isUsingTimeRange) {
          result.timeRangeInfo = {
            type: (startTime || endTime) ? 'custom' : 'week',
            weekType: weekType || null,
            ...dateRange
          };
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      });
  };

  // Helper function để lấy tên ngày
  const getDayName = (dayOfWeek) => {
    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    return days[dayOfWeek];
  };

  async.waterfall([checkParams, getDutyShifts], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
