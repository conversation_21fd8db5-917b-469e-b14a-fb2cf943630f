const _ = require('lodash');
const async = require('async');
const StatisticsTrigger = require('../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');
const DutySpecializedScheduleModel = require('../../../models/dutySpecializedSchedule');
const DutyCriminalScheduleModel = require('../../../models/dutyCriminalSchedule');
const DutyMainScheduleModel = require('../../../models/dutyMainSchedule');
const DutySubScheduleModel = require('../../../models/dutySubSchedule');
const DutyLocationScheduleModel = require('../../../models/dutyLocationSchedule');
const DutyPatrolScheduleModel = require('../../../models/dutyPatrolSchedule');
const DutyStadiumScheduleModel = require('../../../models/dutyStadiumSchedule');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');
const PushNotifyManager = require('../../../jobs/pushNotify');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const _id = _.get(req, 'body._id', '');
  const type = _.get(req, 'body.type', '');
  const users = [];

  const typeArr = [ 
    'dutySpecializedSchedule',
    'dutyCriminalSchedule',
    'dutyMainSchedule',
    'dutySubSchedule',
    'dutyLocationSchedule',
    'dutyPatrolSchedule',
    'dutyStadiumSchedule',
    'dutyEmergencySchedule'
  ]
  let dutyName = '';

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    if(typeArr.indexOf(type) === -1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp loại lịch hợp lệ'
        }
      });
    }
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực'
        }
      });
    }
 
    next();
  };

  const findShift = (next) => {
    let objFind = {
      status: 1
    }
    objFind[`${type}`] = _id;
    
    DutyShiftModel
      .find(objFind)
      .lean()
      .exec((err, shifts) => {
        if (err) {
          return next(err);
        }

        if (!shifts || shifts.length === 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy ca trực liên quan đến lịch trực này'
            }
          });
        }
        // Lấy danh sách người dùng từ các ca trực
        shifts.forEach(shift => {
          if (shift.officer && shift.officer._id && !users.includes(shift.officer._id.toString())) {
            users.push(shift.officer._id.toString());
          }
        });
        next();
      })
  }

  const pushNotify = (next) => {
    if (users.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có cán bộ nào liên quan đến lịch trực này'
        }
      });
    }
    const title = 'Thông báo lịch trực';
    const description = `Cán bộ có lịch trực mới. Vui lòng kiểm tra ứng dụng để biết thêm chi tiết.`;
    const data = {
      link: '',
      extras: {
      }
    };
    PushNotifyManager
      .sendAll({
        _id: {
          $in: users
        }
      }, title, description, data)
      .then((result) => {
        next({
          code: CONSTANTS.CODE.SUCCESS,
          message: MESSAGES.SYSTEM.SUCCESS,
          data: result.data
        });
      })
      .catch(err => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      });
  }

  async.waterfall([
    checkParams,
    findShift,
    pushNotify
  ], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
