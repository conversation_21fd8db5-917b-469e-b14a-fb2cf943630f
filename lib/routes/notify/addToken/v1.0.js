const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../const')
const Notifications = require('../../../models/notification')
const redisConnections = require('../../../connections/redis')


module.exports = (req, res) => {
  res.json({
    code: CONSTANTS.CODE.SUCCESS
  });

  const platform = _.get(req, 'body.platform', '');
  const notify_token = _.get(req, 'body.notify_token', '');
  const memberToken = _.get(req, 'body.memberToken', '');
  const appName = _.get(req, 'body.appName', '');
  const isSafari = _.get(req, 'body.isSafari', 0)
  let member;

  if (!platform || !notify_token) {
    return;
  }

  const getUserId = (next) => {
    if (!memberToken) {
      return next();
    }

    redisConnections('master').getConnection().get(`${appName}:${memberToken}`, (err, result) => {
      if (err || !result) {
        return next();
      }

      try {
        const objSign = JSON.parse(result);
        if (!_.has(objSign, 'id')) {
          return next();
        }

        member = objSign.id;

        next();
      } catch (e) {
        return next();
      }
    });
  }

  const addToken = (next) => {
    const getFollowToken = (done) => {
      Notifications
        .findOne({ notify_token, platform })
        .lean()
        .exec(done)
    }

    const getFollowMember = (done) => {
      if (!member) {
        return done();
      }

      Notifications
        .findOne({ member })
        .lean()
        .exec(done)
    }

    async.parallel([
      getFollowToken,
      getFollowMember
    ], (err, results) => {
      if (!err) {
        const followToken = results[0];
        const followMember = results[1];
        const currentTime = Date.now();


        if (!followToken && !followMember) {
          const objCreate = { notify_token, platform, isSafari };
          if (member) {
            objCreate.member = member;
          }

          Notifications
            .create(objCreate, (err, result) => { })
        } else if (followToken && !followMember) {
          if (member) {
            Notifications.update({ _id: followToken._id }, { member, updatedAt: currentTime }).exec();
          }
        } else if (!followToken && followMember) {
          Notifications
            .create({ member, notify_token, platform, isSafari }, (err, result) => { });
          Notifications
            .update({ _id: followMember._id }, { $unset: { member: 1 } }).exec();
        } else {
          if (followMember.notify_token !== notify_token || followMember.platform !== platform) {
            Notifications
              .update({ _id: followMember._id }, { $unset: { member: 1 } }).exec();
            Notifications.update({ _id: followToken._id }, { member, updatedAt: currentTime }).exec();
          }
        }
      }

      next();
    })
  }


  async.waterfall([
    getUserId,
    addToken
  ], (err, data) => {

  })
}
