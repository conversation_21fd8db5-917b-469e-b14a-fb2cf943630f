const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const SavedNotificationModel = require('../../../models/savedNotification')
const UserModel = require('../../../models/user')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  
  const page = parseInt(_.get(req, 'body.page', 1));
  const limit = parseInt(_.get(req, 'body.limit', 20));
  
  // Validate parameters
  if (page < 1 || limit < 1 || limit > 100) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }

  async.waterfall([
    // Bước 1: <PERSON><PERSON><PERSON> thông tin user để có units
    (callback) => {
      UserModel.findById(userId).select('units').exec((err, user) => {
        if (err) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        if (!user) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        callback(null, user.units || []);
      });
    },
    
    // Bước 2: Xây dựng query điều kiện
    (userUnits, callback) => {
      let query = { status: 1 }; // Chỉ lấy thông báo đang hoạt động
      // Lấy tất cả thông báo dành cho user này
      query = {
        $and: [
          { status: 1 },
          {
            $or: [
              { type: 'all' },
              { type: 'user', users: userId },
              { type: 'unit', units: { $in: userUnits } }
            ]
          }
        ]
      };
      callback(null, query);
    },
    
    // Bước 3: Đếm tổng số bản ghi
    (query, callback) => {
      SavedNotificationModel.countDocuments(query, (err, total) => {
        if (err) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        callback(null, query, total);
      });
    },
    
    // Bước 4: Lấy danh sách thông báo với phân trang
    (query, total, callback) => {
      const skip = (page - 1) * limit;
      
      SavedNotificationModel
        .find(query)
        .select('title image data seen createdAt updatedAt type')
        .sort({ createdAt: -1 }) // Sắp xếp theo thời gian tạo mới nhất
        .skip(skip)
        .limit(limit)
        .exec((err, notifications) => {
          if (err) {
            return callback({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          
          // Thêm thông tin đã đọc cho từng thông báo
          const notificationsWithReadStatus = notifications.map(notification => {
            const notif = notification.toObject();
            notif.hasSeen = notification.seen.includes(userId);
            delete notif.seen; // Xóa field seen khỏi kết quả trả về
            return notif;
          });
          
          const result = {
            code: CONSTANTS.CODE.SUCCESS,
            data: notificationsWithReadStatus,
            pagination: {
              currentPage: page,
              totalPages: Math.ceil(total / limit),
              totalItems: total,
              itemsPerPage: limit,
              hasNextPage: page < Math.ceil(total / limit),
              hasPrevPage: page > 1
            }
          };
          
          callback(null, result);
        });
    }
    
  ], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      };
    }

    res.json(data || err);
  });
}
