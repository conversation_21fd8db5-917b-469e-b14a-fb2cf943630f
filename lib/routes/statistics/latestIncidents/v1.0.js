const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const Report = require('../../../models/report');
const JobType = require('../../../models/jobType');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy 3 vụ việc mới nhất (Latest Incidents)
 * POST /api/v1.0/statistics/latest-incidents
 *
 * Trả về 3 vụ việc mới nhất từ trường metrics của các báo cáo có jobType với chartTypes chứa "highlight"
 * Logic: Lấy vụ việc từ metrics của báo cáo mới nhất trước, nếu không đủ 3 thì lấy thêm từ báo cáo tiếp theo
 * <PERSON><PERSON> thể filter theo khu vực thông qua query parameter
 * Dữ liệu này được sử dụng để hiển thị các vụ việc nổi bật mới nhất trên dashboard
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    area // ID khu vực để filter (optional)
  } = req.body;

  let highlightJobTypes = [];
  let latestIncidents = [];
  let allReports = [];
  const MAX_INCIDENTS = 3;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      area: Joi.objectId().optional() // Khu vực filter là optional
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Tham số không hợp lệ: ${error.details[0].message}`
        }
      });
    }

    next();
  };

  /**
   * Lấy danh sách JobType có chartTypes chứa "highlight"
   */
  const getHighlightJobTypes = (next) => {
    JobType.find({
      'quickReportTemplate.chartTypes': 'highlight',
      status: 1,
      deletedAt: { $exists: false }
    })
    .select('_id name quickReportTemplate')
    .lean()
    .exec((err, jobTypes) => {
      if (err) {
        return next(err);
      }

      if (!jobTypes || jobTypes.length === 0) {
        // Nếu không có JobType nào có chartTypes "highlight", trả về response hoàn chỉnh
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            incidents: [],
            total: 0,
            maxLimit: MAX_INCIDENTS,
            filter: {
              area: area || null,
              chartType: 'highlight'
            },
            generatedAt: Date.now(),
            message: 'Không có loại công việc nào được đánh dấu là highlight'
          }
        });
      }

      highlightJobTypes = jobTypes;
      next();
    });
  };

  /**
   * Lấy tất cả báo cáo có jobType highlight để xử lý metrics
   */
  const getAllHighlightReports = (next) => {
    if (highlightJobTypes.length === 0) {
      return next(); // Skip nếu không có highlight job types
    }

    const mongoose = require('mongoose');
    const highlightJobTypeIds = highlightJobTypes.map(jt => jt._id);

    // Tạo query cơ bản
    const query = {
      jobType: { $in: highlightJobTypeIds },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    // Thêm filter theo khu vực nếu có
    if (area) {
      try {
        const areaObjectId = mongoose.Types.ObjectId(area);
        query['details.location.areas'] = areaObjectId;
      } catch (err) {
        // Nếu area ID không hợp lệ, bỏ qua filter
        console.warn('Invalid area ObjectId:', area);
      }
    }

    Report.find(query)
      .populate({
        path: 'jobType',
        select: 'name description quickReportTemplate'
      })
      // .populate({
      //   path: 'createdBy',
      //   select: 'name idNumber units areas',
      //   populate: [
      //     {
      //       path: 'units',
      //       select: 'name'
      //     },
      //     {
      //       path: 'areas',
      //       select: 'name level'
      //     }
      //   ]
      // })
      .populate({
        path: 'details.location.areas',
        select: 'name level'
      })
      // .populate({
      //   path: 'unit',
      //   select: 'name'
      // })
      .sort({ createdAt: -1 }) // Sắp xếp theo thời gian tạo mới nhất
      .lean()
      .exec((err, reports) => {
        if (err) {
          return next(err);
        }

        allReports = reports || [];
        next();
      });
  };

  /**
   * Xử lý metrics để lấy 3 vụ việc mới nhất
   */
  const processIncidentsFromMetrics = (next) => {
    if (allReports.length === 0) {
      return next();
    }

    const incidents = [];

    // Duyệt qua từng báo cáo theo thứ tự thời gian mới nhất
    for (const report of allReports) {
      if (incidents.length >= MAX_INCIDENTS) {
        break; // Đã đủ 3 vụ việc
      }

      // Kiểm tra metrics của báo cáo
      if (report.metrics && typeof report.metrics === 'object') {
        const jobTypeMetrics = report.jobType?.quickReportTemplate?.metrics || {};

        // Lấy các metric có needsDetails = true (là vụ việc cần chi tiết)
        Object.keys(report.metrics).forEach(metricKey => {
          if (incidents.length >= MAX_INCIDENTS) {
            return; // Đã đủ 3 vụ việc
          }

          const metricValue = report.metrics[metricKey];
          const metricConfig = jobTypeMetrics[metricKey];

          // Chỉ lấy metric có needsDetails = true và có giá trị > 0
          if (metricConfig && metricConfig.needsDetails &&
              typeof metricValue === 'number' && metricValue > 0) {

            // Tạo vụ việc từ metric này
            for (let i = 0; i < metricValue && incidents.length < MAX_INCIDENTS; i++) {
              const incident = createIncidentFromMetric(report, metricKey, metricConfig, i + 1);
              incidents.push(incident);
            }
          }
        });
      }
    }

    latestIncidents = incidents;
    next();
  };

  /**
   * Format dữ liệu response
   */
  const formatResponse = (next) => {
    // latestIncidents đã được format sẵn từ processIncidentsFromMetrics
    const responseData = {
      incidents: latestIncidents,
      total: latestIncidents.length,
      maxLimit: MAX_INCIDENTS,
      filter: {
        area: area || null,
        chartType: 'highlight'
      },
      generatedAt: Date.now()
    };

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: responseData
    });
  };

  /**
   * Ghi log hoạt động
   */
  const writeLog = (result, next) => {
    // Ghi log hoạt động nếu có SystemLogModel
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_latest_incidents',
        description: 'Xem 3 vụ việc mới nhất',
        data: req.body,
        updatedData: {
          totalIncidents: result.data.total,
          hasAreaFilter: !!area,
          areaId: area,
          highlightJobTypesCount: highlightJobTypes.length
        }
      }, () => {});
    }

    next(null, result);
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getHighlightJobTypes,
    getAllHighlightReports,
    processIncidentsFromMetrics,
    formatResponse,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

/**
 * Helper function: Tạo vụ việc từ metric của báo cáo
 * @param {Object} report - Báo cáo gốc
 * @param {String} metricKey - Key của metric
 * @param {Object} metricConfig - Cấu hình metric từ JobType
 * @param {Number} incidentIndex - Số thứ tự vụ việc trong metric
 * @returns {Object} Vụ việc đã format
 */
function createIncidentFromMetric(report, metricKey, metricConfig, incidentIndex) {
  // Tìm thời gian và địa điểm từ details nếu có
  const detailInfo = report.details && report.details[incidentIndex - 1] ? report.details[incidentIndex - 1] : null;
  const incidentTime = detailInfo?.time || report.createdAt;

  return {
    // ID duy nhất cho vụ việc (kết hợp report ID + index)
    _id: `${report._id}_${incidentIndex}`,

    // Thông tin cơ bản
    title: `${report.title || metricConfig.label || metricKey} xảy ra tại ${detailInfo?.location?.address || detailInfo?.location?.areas?.[detailInfo?.location?.areas.length - 1]?.name || 'chưa xác định'}`,
    // description: `Vụ việc ${metricConfig.label || metricKey} từ báo cáo: ${report.title || 'Không có tiêu đề'}`,

    // Thông tin từ báo cáo gốc
    reportId: report._id,
    // reportType: report.reportType,
    // caseCode: report.caseCode,
    // workStatus: report.workStatus,
    // status: report.status,

    // Thông tin metric
    // metricKey: metricKey,
    // metricLabel: metricConfig.label || metricKey,
    // metricValue: report.metrics[metricKey],
    // incidentIndex: incidentIndex,

    // Thông tin loại công việc
    // jobType: {
    //   _id: report.jobType._id,
    //   name: report.jobType.name,
    //   description: report.jobType.description,
    //   chartTypes: report.jobType.quickReportTemplate?.chartTypes || []
    // },

    // Thông tin người tạo
    // createdBy: {
    //   _id: report.createdBy._id,
    //   name: report.createdBy.name,
    //   idNumber: report.createdBy.idNumber,
    //   units: report.createdBy.units || [],
    //   areas: report.createdBy.areas || []
    // },

    // Thông tin đơn vị
    // unit: report.unit ? {
    //   _id: report.unit._id,
    //   name: report.unit.name
    // } : null,

    // Thông tin địa điểm (nếu có)
    location: detailInfo?.location ? {
      address: detailInfo.location.address || '',
      coordinates: detailInfo.location.coordinates || null,
      areas: detailInfo.location.areas || []
    } : null,

    // Thời gian vụ việc
    incidentTime: incidentTime,
    createdAt: report.createdAt,
    updatedAt: report.updatedAt,

    // Thời gian hiển thị dễ đọc
    timeAgo: getTimeAgo(incidentTime)
  };
}

/**
 * Helper function: Tính toán thời gian "time ago" dễ đọc
 * @param {Number} timestamp - Timestamp tạo báo cáo
 * @returns {String} Chuỗi mô tả thời gian
 */
function getTimeAgo(timestamp) {
  const now = Date.now();
  const diff = now - timestamp;

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return 'Vừa xong';
  } else if (minutes < 60) {
    return `${minutes} phút trước`;
  } else if (hours < 24) {
    return `${hours} giờ trước`;
  } else if (days < 7) {
    return `${days} ngày trước`;
  } else {
    return new Date(timestamp).toLocaleDateString('vi-VN');
  }
}
