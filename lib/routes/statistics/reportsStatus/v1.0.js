const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê báo cáo theo trạng thái chi tiết
 * POST /api/v1.0/statistics/reports-status
 *
 * Tr<PERSON> về thống kê chi tiết về trạng thái báo cáo trong hệ thống
 * <PERSON>o gồm phân tích workflow, thời gian xử lý và hiệu suất
 * Dữ liệu này được sử dụng để hiển thị dashboard quản lý và giám sát
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: error.details[0].message
        }
      });
    }

    next();
  };

  /**
   * Lấy thống kê báo cáo theo trạng thái từ service
   */
  const getReportsStatusStats = (next) => {
    try {
      statisticsService.getReportsStatusStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = serviceResult;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: result.message,
      data: result.data
    });

    // Ghi log hoạt động
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_reports_status_stats',
        description: 'Xem thống kê báo cáo theo trạng thái',
        data: req.body,
        updatedData: {
          totalReports: result.data?.statusStats?.total || 0,
          approvalRate: result.data?.workflowStats?.approvalRate || 0,
          completionRate: result.data?.workflowStats?.completionRate || 0,
          averageProcessingTime: result.data?.processingTime?.averageProcessingTime || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getReportsStatusStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
