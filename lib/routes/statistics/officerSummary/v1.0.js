const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê tổng số lượng cán bộ
 * POST /api/v1.0/statistics/officer-summary
 *
 * Tr<PERSON> về thống kê tổng quan về số lượng cán bộ theo các trạng thái khác nhau
 * Bao gồm: đang làm việc, đang trực ban, nghỉ phép, vắng mặt, không có lịch
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: error.details[0].message
        }
      });
    }

    next();
  };

  /**
   * Lấy thống kê tổng số lượng cán bộ từ service
   */
  const getOfficerSummaryStats = (next) => {
    try {
      statisticsService.getOfficerSummaryStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = serviceResult;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_officer_summary_stats',
        description: 'Xem thống kê tổng số lượng cán bộ',
        data: req.body,
        updatedData: {
          totalOfficers: result.data?.summary?.totalOfficers || 0,
          workingOfficers: result.data?.summary?.working || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getOfficerSummaryStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
