const _ = require('lodash')
const async = require('async')
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi)
const Report = require('../../../models/report')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const StatisticsTrigger = require('../../../utils/statisticsTrigger')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const {
    reportId,
    caseCode,
    workStatus
  } = req.body

  let report;
  let oldReport;

  const validateParams = (next) => {
    const schema = Joi.object().keys({
      reportId: Joi.objectId().optional(),
      caseCode: Joi.string().optional(),
      workStatus: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled', 'on_hold').required()
    }).or('reportId', 'caseCode')

    Joi.validate(req.body, schema, (err, value) => {
      if (err) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        })
      }

      next()
    })
  }

  const updateReport = (next) => {
    const query = {
      workStatus: {$nin: ['completed', 'cancelled']}
    }
    if (reportId) {
      query._id = reportId
    }
    if (caseCode) {
      query.caseCode = caseCode
    }

    // Lấy dữ liệu cũ trước khi update
    Report.findOne(query)
    .lean()
    .exec((err, oldReportData) => {
      if (err) {
        return next(err)
      }

      if (!oldReportData) {
        return next({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: {
            head: 'Thông báo',
            body: 'Không tìm thấy báo cáo. Vui lòng kiểm tra lại.'
          }
        })
      }

      oldReport = oldReportData;

      // Thực hiện update
      Report.findOneAndUpdate(query, {
        workStatus
      }, {
        new: true
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        report = result
        next()
      })
    })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: report,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật báo cáo thành công'
      }
    })

    // Trigger statistics update cho document reports
    try {
      if (oldReport && report) {
        StatisticsTrigger.triggerDocumentUpdate('update', report, oldReport);
      }
    } catch (error) {
      console.error('Error triggering document update:', error);
    }

    // Log system action (nếu có SystemLogModel)
    if (global.SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'update_report',
        description: 'Cập nhật báo cáo',
        data: req.body,
        updatedData: report
      }, () => { })
    }
  }

  async.waterfall([
    validateParams,
    updateReport,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}