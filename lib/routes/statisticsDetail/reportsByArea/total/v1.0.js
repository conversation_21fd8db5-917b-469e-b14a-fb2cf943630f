const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const AreaModel = require('../../../../models/area');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê tổng quan vụ việc theo khu vực
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê tổng quan về vụ việc theo khu vực cụ thể
 * Sử dụng trường summary trong ReportModel để đếm số vụ việc
 * Phân chia theo thời gian và JobType
 * Dữ liệu này được sử dụng để hiển thị dashboard thống kê theo khu vực
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const areaId = req.body.area;
  const options = [
    '3days','7days','week','month','year',
  ]

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;
  let areaInfo;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    if(!areaId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số area là bắt buộc'
        }
      });
    }
    if(!mongoose.Types.ObjectId.isValid(areaId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số area không hợp lệ'
        }
      });
    }
    next();
  };

  const checkAreaExists = (next) => {
    // Kiểm tra xem area có tồn tại không
    AreaModel.findById(areaId)
      .then((area) => {
        if (!area) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,    
            message: {
              head: 'Lỗi tham số',
              body: 'Khu vực không tồn tại'
            }
          });
        }
        // Lưu thông tin area để sử dụng sau
        areaInfo = area;
        next();
      })
      .catch((err) => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Lỗi hệ thống',
            body: 'Đã xảy ra lỗi khi kiểm tra khu vực'
          }
        });
      });
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
          }
        });
    }

    next();
  };

  const getStatisticDocument = (next) => {
    // B1: Query JobTypeModel để lấy các jobType có chartTypes = 'document'
    JobTypeModel.find({ status : 1, 'quickReportTemplate.chartTypes' : 'heatmap' }, '_id name quickReportTemplate')
      .lean()
      .then((jobTypes) => {
        if (!jobTypes || jobTypes.length === 0) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGES.SYSTEM.SUCCESS,
            data: {
              totalReports: 0,
              metrics: []
            }
          });
        }

        const jobTypeIds = jobTypes.map(jt => jt._id);
        
        // B2: Query ReportModel để lấy thống kê
        const reportQuery = {
          jobType: { $in: jobTypeIds },
          'summary.area': mongoose.Types.ObjectId(areaId),
          createdAt: {
            $gte: startTime.getTime(),
            $lte: new Date(endTime).getTime()
          }
        };
        
        // Tạo pipeline aggregate dựa vào type để phân chia thời gian
        let dateGrouping;
        let sortField;
        
        switch (type) {
          case '3days':
          case '7days':
            // Chia theo từng ngày
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              month: { $month: { $toDate: '$createdAt' } },
              day: { $dayOfMonth: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.jobType': 1 };
            break;
          case 'week':
            // Chia theo từng thứ trong tuần (1=Chủ nhật, 2=Thứ 2, ...)
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              week: { $week: { $toDate: '$createdAt' } },
              dayOfWeek: { $dayOfWeek: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.week': 1, '_id.dayOfWeek': 1, '_id.jobType': 1 };
            break;
          case 'month':
            // Chia theo từng tuần trong tháng
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              month: { $month: { $toDate: '$createdAt' } },
              week: { $week: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.jobType': 1 };
            break;
          case 'year':
            // Chia theo từng tháng trong năm
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              month: { $month: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.jobType': 1 };
            break;
        }

        ReportModel.aggregate([
          { $match: reportQuery },
          {
            $unwind: '$summary'
          },
          {
            $match: {
              'summary.area': mongoose.Types.ObjectId(areaId)
            }
          },
          {
            $group: {
              _id: {
                jobType: '$jobType',
                ...dateGrouping
              },
              totalReports: { $sum: '$summary.count' }
            }
          },
          { $sort: sortField }
        ])
          .then((reportStats) => {
            // Helper function để tạo tất cả time periods cho type
            const generateAllTimePeriods = () => {
              const periods = [];
              const start = new Date(startTime);
              const end = new Date(endTime);
              
              switch (type) {
                case '3days':
                case '7days':
                  // Tạo tất cả các ngày trong khoảng thời gian
                  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                    periods.push({
                      timeLabel: `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}`,
                      period: {
                        year: d.getFullYear(),
                        month: d.getMonth() + 1,
                        day: d.getDate()
                      }
                    });
                  }
                  break;
                case 'week':
                  // Tạo tất cả các thứ trong tuần (T2 -> CN)
                  const dayNames = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
                  for (let i = 0; i < 7; i++) {
                    periods.push({
                      timeLabel: dayNames[i],
                      period: {
                        dayOfWeek: i === 6 ? 0 : i + 1 // T2=1, T3=2, ..., T7=6, CN=0
                      }
                    });
                  }
                  break;
                case 'month':
                  // Tạo tất cả các tuần trong tháng
                  const startWeek = Math.floor((start.getTime() - new Date(start.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
                  const endWeek = Math.floor((end.getTime() - new Date(end.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
                  for (let w = startWeek; w <= endWeek; w++) {
                    periods.push({
                      timeLabel: `Tuần ${w - startWeek + 1}`,
                      period: {
                        week: w
                      }
                    });
                  }
                  break;
                case 'year':
                  // Tạo tất cả các tháng trong năm
                  for (let m = start.getMonth() + 1; m <= end.getMonth() + 1; m++) {
                    periods.push({
                      timeLabel: `T${m}`,
                      period: {
                        month: m
                      }
                    });
                  }
                  break;
              }
              return periods;
            };

            // Helper function để format label thời gian
            const formatTimeLabel = (timeGroup) => {
              switch (type) {
                case '3days':
                case '7days':
                  return `${timeGroup.day.toString().padStart(2, '0')}/${timeGroup.month.toString().padStart(2, '0')}`;
                case 'week':
                  const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
                  return dayNames[timeGroup.dayOfWeek];
                case 'month':
                  return `Tuần ${timeGroup.week - Math.floor((new Date(timeGroup.year, timeGroup.month - 1, 1).getTime() - new Date(timeGroup.year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}`;
                case 'year':
                  return `T${timeGroup.month}`;
                default:
                  return '';
              }
            };

            // Helper function để tính startTime và endTime cho từng time period
            const calculatePeriodTimeRange = (period) => {
              let periodStart, periodEnd;
              
              switch (type) {
                case '3days':
                case '7days':
                  // Cho từng ngày
                  periodStart = new Date(period.year, period.month - 1, period.day, 0, 0, 0);
                  periodEnd = new Date(period.year, period.month - 1, period.day, 23, 59, 59);
                  break;
                case 'week':
                  // Cho từng thứ trong tuần (T2 -> CN)
                  const weekStart = new Date(startTime);
                  periodStart = new Date(weekStart);
                  // Tính offset: T2=0, T3=1, ..., T7=5, CN=6
                  const offset = period.dayOfWeek === 0 ? 6 : period.dayOfWeek - 1;
                  periodStart.setDate(weekStart.getDate() + offset);
                  periodStart.setHours(0, 0, 0, 0);
                  periodEnd = new Date(periodStart);
                  periodEnd.setHours(23, 59, 59, 999);
                  break;
                case 'month':
                  // Cho từng tuần trong tháng
                  const yearStart = new Date(startTime.getFullYear(), 0, 1);
                  periodStart = new Date(yearStart.getTime() + (period.week - 1) * 7 * 24 * 60 * 60 * 1000);
                  periodEnd = new Date(periodStart.getTime() + 6 * 24 * 60 * 60 * 1000);
                  periodEnd.setHours(23, 59, 59, 999);
                  break;
                case 'year':
                  // Cho từng tháng trong năm
                  periodStart = new Date(startTime.getFullYear(), period.month - 1, 1, 0, 0, 0);
                  periodEnd = new Date(startTime.getFullYear(), period.month, 0, 23, 59, 59);
                  break;
                default:
                  periodStart = new Date(startTime);
                  periodEnd = new Date(endTime);
              }
              
              return {
                startTime: periodStart.getTime(),
                endTime: periodEnd.getTime()
              };
            };

            // Tạo tất cả time periods
            const allTimePeriods = generateAllTimePeriods();

            // Tạo một object để lưu tổng số vụ việc cho mỗi time period
            const timePeriodsData = {};
            
            // Khởi tạo tất cả time periods với giá trị 0
            allTimePeriods.forEach(period => {
              const timeRange = calculatePeriodTimeRange(period.period);
              timePeriodsData[period.timeLabel] = {
                timeLabel: period.timeLabel,
                period: period.period,
                startTime: timeRange.startTime,
                endTime: timeRange.endTime,
                totalReports: 0
              };
            });
            
            // Cập nhật dữ liệu thực tế từ reportStats (tổng hợp tất cả jobTypes)
            reportStats.forEach(stat => {
              const timeLabel = formatTimeLabel(stat._id);
              if (timePeriodsData[timeLabel]) {
                timePeriodsData[timeLabel].totalReports += stat.totalReports;
              }
            });

            // Tính tổng số báo cáo
            const totalReports = reportStats.reduce((sum, stat) => sum + stat.totalReports, 0);

            // Convert thành array
            const metrics = Object.values(timePeriodsData);
            let color = '#007CFE'
            if(areaInfo.name === 'Hùng Vương') {
              color = '#007CFE'; 
            } else if(areaInfo.name === 'Thượng Lý') {
              color = '#33C5E8'; 
            } else if(areaInfo.name === 'Ngô Quyền') {
              color = '#F9DEDC'; 
            } else if(areaInfo.name === 'Sở Dầu') {
              color = '#D30500'; 
            } else if(areaInfo.name === 'Phan Bội Châu') {
              color = '#6750A4'; 
            } else if(areaInfo.name === 'Gia Viên') {
              color = '#6750A4'; 
            } else if(areaInfo.name === 'Minh Khai') {
              color = '#e74712ff'; 
            } 
            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: `Tổng vụ việc tại ${areaInfo.name}`,
                summary: {
                  totalReports: totalReports,
                  areaName: areaInfo.name
                },
                chartConfig: {
                  colors: {
                    totalReports: color    // Xanh dương sáng - tổng số vụ việc
                  },
                  labels: {
                    totalReports: 'Tổng số vụ việc'
                  }
                },
                metrics,
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    checkAreaExists,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
