const _ = require("lodash")
const async = require("async")
const Joi = require("joi")
Joi.objectId = require("joi-objectid")(Joi)

const User = require("../../../../models/user")
const CONSTANTS = require("../../../../const")
const MESSAGES = require("../../../../message")

// Import utility function để xử lý cấu trúc phân cấp areas
const { processAreasHierarchy } = require('../../../../util/areasHierarchy');

module.exports = (req, res) => {
 const { id } = req.body || ""
 const checkParams = (next) => {
  if (!id) {
   return next({
    code: CONSTANTS.CODE.WRONG_PARAMS,
    message: MESSAGES.SYSTEM.WRONG_PARAMS,
   })
  }
  next(null)
 }

 const getUserInf = (next) => {
  User.findOne({
    _id: id,
    status: 1
  }, "-password")
    .populate('permissions', 'name')
    .populate({
      path: 'units',
      select: 'name parentPath',
      populate: {
        path: 'parentPath',
        select: 'name'
      }
    })
    .populate('position', 'name unit')
    .populate('categories', 'name icon')
    .populate('jobTypes', 'name')
    .populate({
      path: 'areas',
      select: 'name level parent parentPath',
      populate: {
        path: 'parent',
        select: 'name'
      }
    })
    .populate({
      path: 'groupPermissions',
      select: 'name permissions',
      populate: {
        path: 'permissions',
        select: 'name'
      }
    })
   .lean()
   .exec((err, result) => {
    if (err) {
     return next(err)
    }

    // Xử lý cấu trúc phân cấp areas nếu user có areas
    if (result && result.areas && result.areas.length > 0) {
      result.areas = processAreasHierarchy(result.areas);
    }

    next(null, {
     code: CONSTANTS.CODE.SUCCESS,
     data: result,
    })
   })
 }

 async.waterfall([checkParams, getUserInf], (err, data) => {
  err &&
   _.isError(err) &&
   (data = {
    code: CONSTANTS.CODE.SYSTEM_ERROR,
    message: MESSAGES.SYSTEM.ERROR,
   })

  res.json(data || err)
 })
}
