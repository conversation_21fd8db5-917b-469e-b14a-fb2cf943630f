const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/statistics
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    sessionId,
    startDate,
    endDate,
    status,
    createdBy
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      sessionId: Joi.objectId().optional(),
      startDate: Joi.string().optional(),
      endDate: Joi.string().optional(),
      status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
      createdBy: Joi.objectId().optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getStatistics = (next) => {
    try {
      let statistics;

      if (sessionId) {
        // Thống kê chi tiết cho một phiên cụ thể
        statistics = suddenAttendanceService.getSessionDetailStatistics(sessionId);
      } else {
        // Thống kê tổng quan
        statistics = suddenAttendanceService.getOverallStatistics({
          startDate,
          endDate,
          status,
          createdBy,
          userId
        });
      }

      statistics
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_sudden_attendance_stats',
        description: 'Xem thống kê chấm công đột xuất',
        data: req.body,
        updatedData: result.data
      }, () => {});
    }
  };

  async.waterfall([
    validateParams,
    getStatistics,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
