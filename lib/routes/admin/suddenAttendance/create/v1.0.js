const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const NotificationHelper = require('../../../../util/notificationHelper');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API tạo phiên chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/create
 */
module.exports = (req, res) => {
  const creatorId = req.user.id;
  const {
    title,
    description,
    startTime,
    validDurationMinutes,
    targetUnits,
    sendNotification = true
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      title: Joi.string().required().trim().max(200),
      description: Joi.string().optional().trim().max(500),
      startTime: Joi.number().required().min(Date.now()),
      validDurationMinutes: Joi.number().optional().min(1).max(60).default(15),
      targetUnits: Joi.array().items(Joi.objectId()).optional(),
      sendNotification: Joi.boolean().optional().default(true)
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const createSession = (next) => {
    try {
      const sessionData = {
        title,
        description,
        startTime,
        validDurationMinutes: validDurationMinutes || 15,
        targetUnits: targetUnits || []
      };

      suddenAttendanceService.createSession(sessionData, creatorId)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const sendNotifications = (next) => {
    if (!sendNotification || !result.success || !result.data) {
      return next();
    }

    try {
      // Gửi thông báo cho các cán bộ được áp dụng
      NotificationHelper.notifyNewSuddenAttendanceSession(
        result.data.targetUsers,
        {
          _id: result.data._id,
          title: result.data.title,
          startTime: result.data.startTime,
          validDurationMinutes: result.data.validDurationMinutes
        }
      );

      next();
    } catch (error) {
      console.error('Error sending notifications:', error);
      // Không fail request nếu gửi thông báo lỗi
      next();
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: creatorId,
        action: 'create_sudden_attendance_session',
        description: 'Tạo phiên chấm công đột xuất',
        data: req.body,
        updatedData: result.data
      }, () => {});
    }
  };

  async.waterfall([
    validateParams,
    createSession,
    sendNotifications,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
