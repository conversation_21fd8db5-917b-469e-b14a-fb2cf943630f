const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const scheduleService = require('../../../../services/scheduleService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API lấy danh sách lịch làm việc được nhóm theo user với phân trang theo users
 * POST /api/v1.0/work-schedule/list
 *
 * Trả về dữ liệu được tổ chức theo từng user với:
 * - Thông tin user (name, idNumber, units, position)
 * - <PERSON><PERSON> sách lịch làm việc của user đó được sắp xếp theo ngày
 * - <PERSON><PERSON> trang theo số lượng users (không phải schedules)
 * - Thống kê tổng quan (tổng số schedules, users, trung bình schedules/user)
 * - Hỗ trợ sắp xếp users theo: tên (lấy từ cuối), tổ (units), chức vụ (position)
 * - Hỗ trợ thứ tự sắp xếp: tăng dần (asc) hoặc giảm dần (desc)
 * - Hỗ trợ lọc theo ca làm việc: morning, afternoon, both (user phải có đủ ca trong TẤT CẢ ngày)
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    startDate,
    endDate,
    userId,
    unitId,
    page = 1,
    limit = 20,
    sortBy = 'name', // Trường sắp xếp: 'name', 'unit', 'position'
    sortOrder = 'asc', // Thứ tự sắp xếp: 'asc' (tăng dần), 'desc' (giảm dần)
    shiftFilter // Lọc theo ca làm việc: 'morning', 'afternoon', 'both'
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      userId: Joi.objectId().optional(),
      unitId: Joi.objectId().optional(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sortBy: Joi.string().valid('name', 'unit', 'position').default('name').optional(),
      sortOrder: Joi.string().valid('asc', 'desc').default('asc').optional(),
      shiftFilter: Joi.string().valid('morning', 'afternoon', 'both').optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai (định dạng DD-MM-YYYY)
    if (startDate && endDate) {
      if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.ATTENDANCE.WRONG_DATE
        });
      }
    }

    next();
  };

  const getScheduleList = (next) => {
    try {
      const filters = {
        startDate,
        endDate,
        userId,
        unitId,
        page,
        limit
      };

      // Sử dụng method mới để phân trang theo users
      scheduleService.getScheduleListByUsers(viewerId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Nhóm dữ liệu lịch làm việc theo user và sắp xếp
   * @param {Array} schedules - Danh sách lịch làm việc
   * @param {String} sortBy - Trường sắp xếp: 'name' (tên), 'unit' (tổ), 'position' (chức vụ)
   * @param {String} sortOrder - Thứ tự sắp xếp: 'asc' (tăng dần), 'desc' (giảm dần)
   * @returns {Array} Dữ liệu được nhóm theo user
   */
  const groupSchedulesByUser = (schedules, sortBy = 'name', sortOrder = 'asc') => {
    // Nhóm schedules theo user._id
    const groupedByUser = _.groupBy(schedules, (schedule) => {
      return schedule.user._id.toString();
    });

    // Chuyển đổi thành array với thông tin user và danh sách schedules
    const userSchedules = Object.keys(groupedByUser).map(userId => {
      const userScheduleList = groupedByUser[userId];
      const userInfo = userScheduleList[0].user; // Lấy thông tin user từ schedule đầu tiên

      // Sắp xếp schedules của user theo ngày
      const sortedSchedules = userScheduleList
        .map(schedule => ({
          _id: schedule._id,
          date: schedule.date,
          shifts: schedule.shifts,
          createdBy: schedule.createdBy,
          createdAt: schedule.createdAt,
          updatedAt: schedule.updatedAt,
          status: schedule.status
        }))
        .sort((a, b) => {
          // Convert DD-MM-YYYY to YYYY-MM-DD để so sánh
          const dateA = DateUtils.convertDDMMYYYYtoYYYYMMDD(a.date);
          const dateB = DateUtils.convertDDMMYYYYtoYYYYMMDD(b.date);

          if (sortOrder === 'asc') {
            return dateA.localeCompare(dateB); // Cũ đến mới
          } else {
            return dateB.localeCompare(dateA); // Mới đến cũ (default)
          }
        });

      return {
        user: {
          _id: userInfo._id,
          name: userInfo.name,
          idNumber: userInfo.idNumber,
          units: userInfo.units,
          position: userInfo.position
        },
        schedules: sortedSchedules,
        totalSchedules: sortedSchedules.length
      };
    });

    // Sắp xếp users theo trường được chọn
    return userSchedules.sort((a, b) => {
      let compareResult = 0;

      switch (sortBy) {
        case 'name':
          // Sắp xếp theo tên: lấy từ cuối cùng (họ tên Việt Nam)
          const getLastName = (fullName) => {
            if (!fullName) return '';
            const nameParts = fullName.trim().split(' ');
            return nameParts[nameParts.length - 1];
          };

          const lastNameA = getLastName(a.user.name);
          const lastNameB = getLastName(b.user.name);
          compareResult = lastNameA.localeCompare(lastNameB, 'vi', { sensitivity: 'base' });
          break;

        case 'unit':
          // Sắp xếp theo tổ (units) - lấy tên unit cuối cùng
          const unitA = (a.user.units && a.user.units.length > 0) ? a.user.units[a.user.units.length - 1].name || '' : '';
          const unitB = (b.user.units && b.user.units.length > 0) ? b.user.units[b.user.units.length - 1].name || '' : '';
          compareResult = unitA.localeCompare(unitB, 'vi', { sensitivity: 'base' });
          break;

        case 'position':
          // Sắp xếp theo chức vụ (position)
          const positionA = (a.user.position && a.user.position.name) ? a.user.position.name : '';
          const positionB = (b.user.position && b.user.position.name) ? b.user.position.name : '';
          compareResult = positionA.localeCompare(positionB, 'vi', { sensitivity: 'base' });
          break;

        default:
          // Mặc định sắp xếp theo tên đầy đủ
          compareResult = a.user.name.localeCompare(b.user.name, 'vi', { sensitivity: 'base' });
      }

      // Áp dụng thứ tự sắp xếp (asc/desc)
      return sortOrder === 'desc' ? -compareResult : compareResult;
    });
  };

  /**
   * Lọc users theo ca làm việc
   * @param {Array} userSchedules - Danh sách user schedules đã được nhóm
   * @param {String} shiftFilter - Loại ca cần lọc: 'morning', 'afternoon', 'both'
   * @returns {Array} Danh sách users đã được lọc
   */
  const filterUsersByShift = (userSchedules, shiftFilter) => {
    if (!shiftFilter) {
      return userSchedules; // Không lọc nếu không có shiftFilter
    }

    return userSchedules.filter(userSchedule => {
      const { schedules } = userSchedule;

      // Kiểm tra từng ngày có lịch làm việc
      return schedules.every(schedule => {
        const shiftTypes = schedule.shifts.map(shift => shift.type);

        switch (shiftFilter) {
          case 'morning':
            // Phải có ca sáng trong ngày này
            return shiftTypes.includes('morning');

          case 'afternoon':
            // Phải có ca chiều trong ngày này
            return shiftTypes.includes('afternoon');

          case 'both':
            // Phải có cả ca sáng VÀ ca chiều trong ngày này
            return shiftTypes.includes('morning') && shiftTypes.includes('afternoon');

          default:
            return true;
        }
      });
    });
  };

  const formatResponse = (next) => {
    try {
      const { schedules, pagination } = result.data;

      // Nhóm dữ liệu theo user với sắp xếp theo trường được chọn
      const groupedData = groupSchedulesByUser(schedules, sortBy, sortOrder);

      // Áp dụng lọc theo ca làm việc nếu có
      const filteredData = filterUsersByShift(groupedData, shiftFilter);

      // Pagination đã được xử lý ở tầng service theo users
      const currentPageUsers = filteredData.length; // Số users trong trang hiện tại sau khi lọc
      const totalUsers = pagination.totalUsers; // Tổng số users có schedules
      const totalSchedules = pagination.total; // Tổng số schedules

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          userSchedules: filteredData,
          pagination: {
            page: pagination.page,
            limit: pagination.limit, // Limit áp dụng cho users
            total: totalSchedules, // Tổng số schedules (tất cả users)
            totalUsers: totalUsers, // Tổng số users có schedules
            pages: pagination.pages, // Tổng số trang (dựa trên users)
            currentPageUsers: currentPageUsers, // Số users trong trang hiện tại
            usersInCurrentPage: pagination.usersInCurrentPage || currentPageUsers
          },
          summary: {
            totalSchedules: totalSchedules,
            totalUsers: totalUsers,
            averageSchedulesPerUser: totalUsers > 0 ? Math.round(totalSchedules / totalUsers * 100) / 100 : 0,
            currentPageSchedules: filteredData.reduce((sum, user) => sum + user.totalSchedules, 0),
            filteredUsers: filteredData.length, // Số users sau khi lọc theo ca
            shiftFilter: shiftFilter || null // Thông tin bộ lọc đã áp dụng
          }
        }
      });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getScheduleList,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};