const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutySpecializedScheduleModel = require('../../../models/dutySpecializedSchedule');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  let { _id, data } = req.body; // _id: ID của schedule, data: array ca trực cho tất cả các ngày trong tuần

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực'
        }
      });
    }

    // Validate data
    if (!Array.isArray(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'data phải là một array'
        }
      });
    }

    // Validate từng ca trực trong data
    const validateData = (dataList) => {
      return dataList.every(shift => {
        return typeof shift.startTime === 'number' && 
               typeof shift.endTime === 'number' &&
               typeof shift.forLeader === 'boolean' &&
               shift.startTime >= 0 &&
               shift.endTime > 0 &&
               shift.startTime < shift.endTime
      });
    };

    if (!validateData(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Cấu trúc data không hợp lệ'
        }
      });
    }
    const leaderShifts = [
      {
          "forLeader": true,
          "startTime": 0,
          "endTime": 86400000
      }
    ]
    data = leaderShifts.concat(data); 
    next();
  };

  const checkExistingDutyShifts = (next) => {
    // Kiểm tra xem có ca trực nào đang tồn tại không
    DutyShiftModel.findOne({
      dutySpecializedSchedule: _id,
      status: 1
    })
    .lean()
    .exec((err, existingShift) => {
      if (err) {
        return next(err);
      }

      if (existingShift) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Đổi ca trực không thành công do đang có cán bộ đã được sắp xếp lịch trực. Vui lòng xóa lịch trực hiện tại để cập nhật lại ca trực'
          }
        });
      }

      next();
    });
  };

  const validateTimeConflicts = (next) => {
    // Kiểm tra xung đột thời gian trong data
    const conflicts = [];
    
    // Kiểm tra xung đột trong cùng một ngày (vì data này là cho tất cả các ngày trong tuần)
    for (let i = 0; i < data.length; i++) {
      if(data[i].forLeader) {
        continue; // Bỏ qua ca trực dành cho leader
      }
      for (let j = i + 1; j < data.length; j++) {
        const shift1 = data[i];
        const shift2 = data[j];
        
        // Kiểm tra trùng lặp thời gian (chỉ cần so sánh số milliseconds trong ngày)
        const isConflict = (
          (shift1.startTime < shift2.endTime && shift1.endTime > shift2.startTime)
        );
        
        if (isConflict) {
          const formatTime = (milliseconds) => {
            const hours = Math.floor(milliseconds / 3600000);
            const minutes = Math.floor((milliseconds % 3600000) / 60000);
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          };
          
          conflicts.push({
            shift1: `${formatTime(shift1.startTime)} - ${formatTime(shift1.endTime)}`,
            shift2: `${formatTime(shift2.startTime)} - ${formatTime(shift2.endTime)}`
          });
        }
      }
    }
    
    if (conflicts.length > 0) {
      const conflictMessages = conflicts.map(conflict => 
        `${conflict.shift1} và ${conflict.shift2}`
      ).join('; ');
      
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Có xung đột thời gian trong ca trực: ${conflictMessages}`
        }
      });
    }
    
    next();
  };

  const updateDutyShiftTemplate = (next) => {
    // Tạo template cho DutyShiftTemplateModel
    const daysOfWeek = [
      'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'
    ];

    const templateData = daysOfWeek.map((dayName) => ({
      name: dayName,
      data: data.map(shift => ({
        startTime: shift.startTime,
        endTime: shift.endTime,
        forLeader: shift.forLeader
        // Không có unit vì đây là template chung
      }))
    }));

    // Cập nhật DutyShiftTemplateModel với điều kiện source=specialized và status=1
    DutyShiftTemplateModel.findOneAndUpdate(
      { 
        source: 'specialized',
        status: 1 
      },
      { 
        $set: { 
          template: templateData,
          updatedAt: Date.now()
        }
      },
      { 
        new: true,
        upsert: true // Tạo mới nếu không tồn tại
      }
    )
    .lean()
    .exec((err, updatedTemplate) => {
      if (err) {
        return next(err);
      }
      
      // Tiếp tục với bước tiếp theo
      next();
    });
  };

  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutySpecializedScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tạo weeklyScheduleTemplate mới cho tất cả các ngày trong tuần
        const daysOfWeek = [
          'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'
        ];

        // Tính toán ngày đầu tuần (Thứ 2) dựa trên startTime của schedule
        const scheduleStartDate = new Date(schedule.startTime);
        const dayOfWeek = scheduleStartDate.getDay(); // 0: Chủ nhật, 1: Thứ 2, ..., 6: Thứ 7
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Số ngày từ ngày hiện tại đến Thứ 2
        const mondayDate = new Date(scheduleStartDate);
        mondayDate.setDate(scheduleStartDate.getDate() - daysToMonday);
        mondayDate.setHours(0, 0, 0, 0); // Set về đầu ngày

        const newWeeklyScheduleTemplate = daysOfWeek.map((dayName, dayIndex) => {
          // Tính ngày cụ thể cho từng ngày trong tuần
          const currentDayDate = new Date(mondayDate);
          currentDayDate.setDate(mondayDate.getDate() + dayIndex);
          const dayStartTimestamp = currentDayDate.getTime();

          // Tạo data với thời gian thực tế cho ngày này
          const dayData = data.map(shift => ({
            ...shift,
            startTime: dayStartTimestamp + shift.startTime,
            endTime: dayStartTimestamp + shift.endTime
          }));

          return {
            _id: new mongoose.Types.ObjectId(),
            name: dayName,
            data: dayData
          };
        });

        // Cập nhật vào database
        DutySpecializedScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: newWeeklyScheduleTemplate,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật lịch trực tuần thành công'
            },
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, checkExistingDutyShifts, validateTimeConflicts, updateDutyShiftTemplate, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
