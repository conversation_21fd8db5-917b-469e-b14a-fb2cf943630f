const statisticsMetadataJob = require('../jobs/statisticsMetadataJob');
const CONSTANTS = require('../const');

/**
 * Utility helper đ<PERSON> trigger statistics events từ các API khác
 * Cung cấp interface đơn giản để các module kh<PERSON>c có thể trigger cập nhật thống kê
 */
class StatisticsTrigger {

  /**
   * Trigger khi có cập nhật ca trực (DutyShift)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} dutyShiftData - Dữ liệu ca trực
   */
  static triggerDutyShiftUpdate(action, dutyShiftData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('duty_shift_updated', {
        action,
        dutyShiftId: dutyShiftData._id || dutyShiftData.id,
        officerId: dutyShiftData.officer,
        startTime: dutyShiftData.startTime,
        endTime: dutyShiftData.endTime,
        status: dutyShiftData.status,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered duty_shift_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering duty_shift_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật lịch trực (8 loại DutySchedule)
   * @param {String} scheduleType - Loại lịch trực: 'main', 'sub', 'location', etc.
   * @param {String} action - Hành động: 'create', 'update', 'delete', 'updateShift', 'updateTemplate'
   * @param {Object} scheduleData - Dữ liệu lịch trực
   */
  static triggerDutyScheduleUpdate(scheduleType, action, scheduleData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('duty_schedule_updated', {
        scheduleType,
        action,
        scheduleId: scheduleData._id || scheduleData.id,
        weeklySchedule: scheduleData.weeklySchedule,
        startTime: scheduleData.startTime,
        endTime: scheduleData.endTime,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered duty_schedule_updated: ${scheduleType} - ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering duty_schedule_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật điểm danh (AttendanceRecord)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} attendanceData - Dữ liệu điểm danh
   */
  static triggerAttendanceUpdate(action, attendanceData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('attendance_updated', {
        action,
        attendanceId: attendanceData._id || attendanceData.id,
        userId: attendanceData.user,
        date: attendanceData.date,
        shift: attendanceData.shift,
        status: attendanceData.status,
        checkinTime: attendanceData.checkinTime,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered attendance_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering attendance_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật lịch làm việc (WorkSchedule)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} scheduleData - Dữ liệu lịch làm việc
   */
  static triggerWorkScheduleUpdate(action, scheduleData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('work_schedule_updated', {
        action,
        scheduleId: scheduleData._id || scheduleData.id,
        userId: scheduleData.user,
        date: scheduleData.date,
        shifts: scheduleData.shifts,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered work_schedule_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering work_schedule_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật thông tin cán bộ (User)
   * @param {String} action - Hành động: 'create', 'update', 'inactive', 'activate'
   * @param {Object} userData - Dữ liệu cán bộ
   */
  static triggerUserUpdate(action, userData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('user_updated', {
        action,
        userId: userData._id || userData.id,
        status: userData.status,
        units: userData.units,
        areas: userData.areas,
        position: userData.position,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered user_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering user_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật đơn xin nghỉ (LeaveRequest)
   * @param {String} action - Hành động: 'create', 'approve', 'reject', 'cancel'
   * @param {Object} leaveData - Dữ liệu đơn xin nghỉ
   */
  static triggerLeaveRequestUpdate(action, leaveData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('leave_request_updated', {
        action,
        leaveRequestId: leaveData._id || leaveData.id,
        userId: leaveData.user,
        type: leaveData.type,
        startDate: leaveData.startDate,
        endDate: leaveData.endDate,
        status: leaveData.status,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered leave_request_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering leave_request_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật khu vực (Area)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} areaData - Dữ liệu khu vực
   */
  static triggerAreaUpdate(action, areaData = {}) {
    try {
      statisticsMetadataJob.triggerEvent('area_updated', {
        action,
        areaId: areaData._id || areaData.id,
        name: areaData.name,
        level: areaData.level,
        status: areaData.status,
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered area_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering area_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật báo cáo (Report)
   * @param {String} action - Hành động: 'create', 'update', 'delete', 'approve', 'reject'
   * @param {Object} reportData - Dữ liệu báo cáo
   */
  static triggerReportUpdate(action, reportData = {}) {
    try {
      // Trigger report_updated event
      statisticsMetadataJob.triggerEvent('report_updated', {
        action,
        reportId: reportData._id || reportData.id,
        reportType: reportData.reportType,
        jobType: reportData.jobType,
        status: reportData.status,
        workStatus: reportData.workStatus,
        createdBy: reportData.createdBy,
        unit: reportData.unit,
        metrics: reportData.metrics,
        areas: reportData.details?.map(d => d.location?.area).filter(Boolean) || [],
        timestamp: Date.now()
      });

      console.log(`[StatisticsTrigger] Triggered report_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering report_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật báo cáo công văn (Report)
   * @param {String} action - Hành động: 'create', 'update', 'delete', 'process', 'complete'
   * @param {Object} reportData - Dữ liệu báo cáo
   * @param {Object} oldReportData - Dữ liệu báo cáo cũ (cho action 'update')
   */
  static triggerDocumentUpdate(action, reportData = {}, oldReportData = null) {
    try {
      // Chỉ xử lý nếu là report công văn
      const DOCUMENT_JOB_TYPE_ID = CONSTANTS.DOCUMENT_JOB_TYPE_ID;
      if (reportData.jobType !== DOCUMENT_JOB_TYPE_ID) {
        return;
      }

      // Trigger event cho statistics metadata
      statisticsMetadataJob.triggerEvent('document_updated', {
        action,
        documentId: reportData._id || reportData.id,
        status: reportData.status,
        createdBy: reportData.createdBy,
        unit: reportData.unit,
        metrics: reportData.metrics,
        oldMetrics: oldReportData?.metrics,
        timestamp: Date.now()
      });

      // Cập nhật document metadata counter
      const documentMetadataService = require('../services/documentMetadataService');

      switch (action) {
        case 'create':
          documentMetadataService.handleReportCreated(reportData);
          break;
        case 'update':
          if (oldReportData) {
            documentMetadataService.handleReportUpdated(oldReportData, reportData);
          }
          break;
        // Có thể thêm xử lý cho 'delete' nếu cần
      }

      console.log(`[StatisticsTrigger] Triggered document_updated: ${action} for document report`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering document_updated:', error);
    }
  }

  /**
   * Trigger multiple events cùng lúc
   * @param {Array} events - Mảng các events: [{ type, action, data }]
   */
  static triggerMultipleEvents(events) {
    try {
      events.forEach(event => {
        const { type, action, data } = event;

        switch (type) {
          case 'duty_shift':
            this.triggerDutyShiftUpdate(action, data);
            break;
          case 'duty_schedule':
            this.triggerDutyScheduleUpdate(data.scheduleType, action, data);
            break;
          case 'attendance':
            this.triggerAttendanceUpdate(action, data);
            break;
          case 'work_schedule':
            this.triggerWorkScheduleUpdate(action, data);
            break;
          case 'user':
            this.triggerUserUpdate(action, data);
            break;
          case 'leave_request':
            this.triggerLeaveRequestUpdate(action, data);
            break;
          case 'area':
            this.triggerAreaUpdate(action, data);
            break;
          case 'report':
            this.triggerReportUpdate(action, data);
            break;
          case 'document':
            this.triggerDocumentUpdate(action, data);
            break;
          default:
            console.warn(`[StatisticsTrigger] Unknown event type: ${type}`);
        }
      });

      console.log(`[StatisticsTrigger] Triggered ${events.length} multiple events`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering multiple events:', error);
    }
  }

  /**
   * Force invalidate cache cho một loại thống kê cụ thể
   * @param {String} type - Loại thống kê: 'on_duty_officers', 'attendance', etc.
   * @param {String} timeRange - Khoảng thời gian (optional)
   */
  static async invalidateCache(type, timeRange = null) {
    try {
      const result = await statisticsMetadataJob.invalidateCache(type, timeRange);
      console.log(`[StatisticsTrigger] Cache invalidated for ${type}:`, result);
      return result;
    } catch (error) {
      console.error(`[StatisticsTrigger] Error invalidating cache for ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Force recalculate metadata ngay lập tức
   */
  static async forceRecalculate() {
    try {
      const result = await statisticsMetadataJob.runMetadataCalculationNow();
      console.log('[StatisticsTrigger] Force recalculation completed:', result);
      return result;
    } catch (error) {
      console.error('[StatisticsTrigger] Error in force recalculation:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Lấy thống kê về trigger events
   */
  static getJobStats() {
    try {
      return statisticsMetadataJob.getJobStats();
    } catch (error) {
      console.error('[StatisticsTrigger] Error getting job stats:', error);
      return { error: error.message };
    }
  }
}

module.exports = StatisticsTrigger;
