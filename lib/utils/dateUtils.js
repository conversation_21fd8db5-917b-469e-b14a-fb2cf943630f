const moment = require('moment');

/**
 * Utility functions để xử lý định dạng ngày tháng
 * Chuyển đổi giữa các định dạng date khác nhau
 */
class DateUtils {
  /**
   * <PERSON>yển đổi từ YYYY-MM-<PERSON> sang DD-MM-YYYY
   * @param {String} dateStr - Ngày định dạng YYYY-MM-DD
   * @returns {String} Ngày định dạng DD-MM-YYYY
   */
  static convertYYYYMMDDtoDDMMYYYY(dateStr) {
    if (!dateStr) return null;

    // Kiểm tra định dạng đầu vào
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      throw new Error(`Định dạng ngày không hợp lệ: ${dateStr}. Cần định dạng YYYY-MM-DD`);
    }

    const [year, month, day] = dateStr.split('-');
    return `${day}-${month}-${year}`;
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON> đổi từ DD-MM-YYYY sang YYYY-MM-DD
   * @param {String} dateStr - Ngày định dạng DD-MM-YYYY
   * @returns {String} Ngày định dạng YYYY-MM-DD
   */
  static convertDDMMYYYYtoYYYYMMDD(dateStr) {
    if (!dateStr) return null;

    // Kiểm tra định dạng đầu vào
    if (!/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
      throw new Error(`Định dạng ngày không hợp lệ: ${dateStr}. Cần định dạng DD-MM-YYYY`);
    }

    const [day, month, year] = dateStr.split('-');
    return `${year}-${month}-${day}`;
  }

  /**
   * Tạo danh sách ngày từ startDate đến endDate (định dạng DD-MM-YYYY)
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Array} Danh sách ngày định dạng DD-MM-YYYY
   */
  static generateDateRangeDDMMYYYY(startDate, endDate) {
    const dates = [];

    // Chuyển về YYYY-MM-DD để xử lý với moment
    const startYYYYMMDD = this.convertDDMMYYYYtoYYYYMMDD(startDate);
    const endYYYYMMDD = this.convertDDMMYYYYtoYYYYMMDD(endDate);

    const start = moment(startYYYYMMDD);
    const end = moment(endYYYYMMDD);

    while (start.isSameOrBefore(end)) {
      // Chuyển về DD-MM-YYYY
      const dateYYYYMMDD = start.format('YYYY-MM-DD');
      dates.push(this.convertYYYYMMDDtoDDMMYYYY(dateYYYYMMDD));
      start.add(1, 'day');
    }

    return dates;
  }

  /**
   * Tạo danh sách ngày từ startDate đến endDate (định dạng YYYY-MM-DD)
   * @param {String} startDate - Ngày bắt đầu (YYYY-MM-DD)
   * @param {String} endDate - Ngày kết thúc (YYYY-MM-DD)
   * @returns {Array} Danh sách ngày định dạng YYYY-MM-DD
   */
  static generateDateRangeYYYYMMDD(startDate, endDate) {
    const dates = [];
    const start = moment(startDate);
    const end = moment(endDate);

    while (start.isSameOrBefore(end)) {
      dates.push(start.format('YYYY-MM-DD'));
      start.add(1, 'day');
    }

    return dates;
  }

  /**
   * Validate định dạng DD-MM-YYYY
   * @param {String} dateStr - Chuỗi ngày cần validate
   * @returns {Boolean} True nếu hợp lệ
   */
  static isValidDDMMYYYY(dateStr) {
    if (!dateStr || typeof dateStr !== 'string') return false;

    // Kiểm tra regex
    if (!/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) return false;

    // Kiểm tra ngày hợp lệ
    const [day, month, year] = dateStr.split('-').map(Number);
    const date = new Date(year, month - 1, day);

    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day;
  }

  /**
   * Validate định dạng YYYY-MM-DD
   * @param {String} dateStr - Chuỗi ngày cần validate
   * @returns {Boolean} True nếu hợp lệ
   */
  static isValidYYYYMMDD(dateStr) {
    if (!dateStr || typeof dateStr !== 'string') return false;

    // Kiểm tra regex
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;

    // Kiểm tra ngày hợp lệ
    const [year, month, day] = dateStr.split('-').map(Number);
    const date = new Date(year, month - 1, day);

    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day;
  }

  /**
   * So sánh hai ngày DD-MM-YYYY
   * @param {String} date1 - Ngày thứ nhất
   * @param {String} date2 - Ngày thứ hai
   * @returns {Number} -1 nếu date1 < date2, 0 nếu bằng, 1 nếu date1 > date2
   */
  static compareDDMMYYYY(date1, date2) {
    const d1 = moment(this.convertDDMMYYYYtoYYYYMMDD(date1));
    const d2 = moment(this.convertDDMMYYYYtoYYYYMMDD(date2));

    if (d1.isBefore(d2)) return -1;
    if (d1.isAfter(d2)) return 1;
    return 0;
  }

  /**
   * Lấy ngày hiện tại theo định dạng DD-MM-YYYY
   * @returns {String} Ngày hiện tại
   */
  static getCurrentDateDDMMYYYY() {
    return moment().format('DD-MM-YYYY');
  }

  /**
   * Lấy ngày hiện tại theo định dạng YYYY-MM-DD
   * @returns {String} Ngày hiện tại
   */
  static getCurrentDateYYYYMMDD() {
    return moment().format('YYYY-MM-DD');
  }

  /**
   * Chuyển đổi array dates từ YYYY-MM-DD sang DD-MM-YYYY
   * @param {Array} dates - Mảng ngày YYYY-MM-DD
   * @returns {Array} Mảng ngày DD-MM-YYYY
   */
  static convertArrayYYYYMMDDtoDDMMYYYY(dates) {
    return dates.map(date => this.convertYYYYMMDDtoDDMMYYYY(date));
  }

  /**
   * Chuyển đổi array dates từ DD-MM-YYYY sang YYYY-MM-DD
   * @param {Array} dates - Mảng ngày DD-MM-YYYY
   * @returns {Array} Mảng ngày YYYY-MM-DD
   */
  static convertArrayDDMMYYYYtoYYYYMMDD(dates) {
    return dates.map(date => this.convertDDMMYYYYtoYYYYMMDD(date));
  }

  /**
   * Chuyển đổi thời gian (HH:mm) thành timestamp ngày hiện tại
   * @param {String} timeStr - Thời gian định dạng HH:mm
   * @returns {Number} Timestamp (milliseconds)
   */
  static convertTimeStringToTimestamp(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(0);
    return date.getTime();
  }
}

module.exports = DateUtils;
