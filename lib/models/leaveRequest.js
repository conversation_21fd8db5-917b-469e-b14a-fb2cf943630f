const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model đơn xin nghỉ phép/đi muộn của cán bộ
 * Quản lý các đơn xin nghỉ, xin đi muộn, nghỉ đột xuất
 */
const LeaveRequestSchema = new mongoose.Schema(
  {
    // Cán bộ tạo đơn
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Loại đơn xin nghỉ
    type: {
      type: String,
      enum: ['leave', 'late_arrival', 'emergency_leave'],
      required: true
    },

    // Ngày bắt đầu nghỉ (format: DD-MM-YYYY)
    startDate: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: '<PERSON><PERSON><PERSON> bắt đầu phải có định dạng DD-MM-YYYY'
      }
    },

    // Ngày kết thúc nghỉ (format: DD-MM-YYYY) - chỉ áp dụng cho type 'leave'
    endDate: {
      type: String,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: 'Ngày kết thúc phải có định dạng DD-MM-YYYY'
      }
    },

    // Ca làm việc (cho đi muộn hoặc nghỉ đột xuất)
    shift: {
      type: String,
      enum: ['morning', 'afternoon', 'both']
    },

    // Số ngày nghỉ
    dayCount: {
      type: Number,
      required: true,
      min: 1
    },

    // Lý do xin nghỉ
    reason: {
      type: String,
      required: true
    },

    // Danh sách URL file đính kèm (sử dụng upload service hiện có)
    attachments: [{
      type: String // URL của file đã upload
    }],

    // Trạng thái đơn xin nghỉ
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },

    // Người duyệt đơn
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },

    // Thời gian duyệt
    approvedAt: {
      type: Number
    },

    // Ghi chú từ người duyệt
    approvalNote: {
      type: String
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
LeaveRequestSchema.index({ user: 1, createdAt: -1 }) // Lịch sử đơn xin nghỉ của user
LeaveRequestSchema.index({ status: 1, createdAt: -1 }) // Đơn theo trạng thái
LeaveRequestSchema.index({ approvedBy: 1, createdAt: -1 }) // Đơn đã duyệt
LeaveRequestSchema.index({ startDate: 1, endDate: 1 }) // Tìm theo khoảng thời gian
LeaveRequestSchema.index({ type: 1, status: 1 }) // Thống kê theo loại và trạng thái

module.exports = mongoConnections("master").model("LeaveRequest", LeaveRequestSchema)
