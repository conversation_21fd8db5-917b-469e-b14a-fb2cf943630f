const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const DutyShiftTemplate = new mongoose.Schema({
  template: [
    {
      name: String, // Tên của ngày trong tuần
      data: [
        {
          unit: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Unit' // Tham chiếu đến mô hình Unit
          },
          startTime: {
            type: Number,
            required: true // Giờ bắt đầu ca (timestamp hoặc số giờ)
          },
          endTime: {
            type: Number,
            required: true // Giờ kết thúc ca
          },
          forLeader: {
            type: Boolean,
            default: false // Có phải là lịch trực cho lãnh đạo hay không
          }
        }
      ]
    }
  ],
  source: {
    type: String
  },
  status: {
    type: Number,
    default: 1 // 1: active, 0: inactive
  },
  // Thông tin tạo và cập nhật
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('DutyShiftTemplate', DutyShiftTemplate);
