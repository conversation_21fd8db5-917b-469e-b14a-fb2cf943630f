const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model lên lịch thông báo cho cán bộ
 * Quản lý các thông báo nhắc nhở về giờ làm việc
 */
const NotificationScheduleSchema = new mongoose.Schema(
  {
    // Cán bộ nhận thông báo
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Loại thông báo
    type: {
      type: String,
      enum: ['reminder_30min', 'reminder_5min', 'work_time'],
      required: true
    },

    // Thời gian gửi thông báo
    scheduleTime: {
      type: Date,
      required: true
    },

    // Nội dung thông báo
    message: {
      type: String,
      required: true
    },

    // Dữ liệu bổ sung cho thông báo (JSON)
    data: {
      type: Schema.Types.Mixed,
      default: {}
    },

    // Trạng thái thông báo
    status: {
      type: String,
      enum: ['pending', 'sent', 'failed'],
      default: 'pending'
    },

    // Thời gian gửi thực tế
    sentAt: {
      type: Date
    },

    // Lỗi nếu gửi thất bại
    error: {
      type: String
    },

    // Số lần thử gửi lại
    retryCount: {
      type: Number,
      default: 0
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
NotificationScheduleSchema.index({ scheduleTime: 1, status: 1 }) // Tìm thông báo cần gửi
NotificationScheduleSchema.index({ user: 1, createdAt: -1 }) // Lịch sử thông báo của user
NotificationScheduleSchema.index({ status: 1, createdAt: -1 }) // Thống kê theo trạng thái
NotificationScheduleSchema.index({ type: 1, scheduleTime: 1 }) // Tìm theo loại và thời gian

module.exports = mongoConnections("master").model("NotificationSchedule", NotificationScheduleSchema)