const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const AreaSchema = new mongoose.Schema(
 {
  name: {
    type: String, // tên khu vực địa bàn
    required: true,
  },
  nameAlias: {
    type: String, // tên khu vực địa bàn được chuẩn hóa
  },
  description: {
    type: String, // mô tả khu vực địa bàn
  },
  level: {
    type: Number, // cấp độ khu vực: 1 = khu vực lớn (parent), 2 = tổ dân phố (child)
    required: true,
    enum: [1, 2],
    default: 1
  },
  parent: {
    type: Schema.Types.ObjectId, // khu vực cha (chỉ áp dụng cho level 2)
    ref: 'Area'
  },
  parentPath: [{
    type: Schema.Types.ObjectId, // đường dẫn phân cấp từ gốc đến node hiện tại
    ref: 'Area'
  }],
  boundaries: {
    type: Schema.Types.Mixed, // ranh giới khu vực địa bàn, có thể là GeoJSON hoặc mảng tọa độ
  },
  boundarySearch: {
    type: Schema.Types.Mixed, // ranh giới khu vực địa bàn đã được chuẩn hóa để tìm kiếm
  },
  routes: [{
    type: String, // danh sách các tuyến đường trong khu vực địa bàn, ví dụ: ['Đường A', 'Đường B']
  }],
  familyCount: {
    type: Number, // số lượng hộ gia đình trong khu vực địa bàn
    default: 0,
  },
  populationCount: {
    type: Number, // số lượng dân cư trong khu vực địa bàn
  },
  area: {
    type: Number, // diện tích khu vực địa bàn tính bằng mét vuông
  },
  status: {
    type: Number,
    default: 1,
  },
  leader: {
    type: String, // Tổ trưởng
  },
  leaderPhone: {
    type: String, // Số điện thoại của tổ trưởng
  },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Area", AreaSchema)
