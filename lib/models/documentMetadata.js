const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema;

/**
 * Model lưu trữ metadata và counter cho hệ thống quản lý công văn
 * Tối ưu hóa việc tính toán thống kê công văn chưa trả lời
 */
const DocumentMetadataSchema = new mongoose.Schema(
  {
    // Loại metadata - hiện tại chỉ có 'unanswered_documents_counter'
    type: {
      type: String,
      enum: ['unanswered_documents_counter'],
      required: true,
      index: true
    },

    // Đơn vị (nếu cần phân chia theo đơn vị)
    unit: {
      type: Schema.Types.ObjectId,
      ref: 'Unit',
      index: true
    },

    // Dữ liệu counter
    counters: {
      // Tổng số công văn đến từ khi hệ thống bắt đầu hoạt động
      totalIncomingDocuments: {
        type: Number,
        default: 0,
        min: 0
      },
      
      // Tổng số công văn đã được trả lời từ khi hệ thống bắt đầu hoạt động
      totalRepliedDocuments: {
        type: Number,
        default: 0,
        min: 0
      },
      
      // Số công văn chưa trả lời (được tính tự động)
      // = totalIncomingDocuments - totalRepliedDocuments
      unansweredDocuments: {
        type: Number,
        default: 0,
        min: 0
      }
    },

    // Thông tin cập nhật cuối cùng
    lastUpdated: {
      type: Number,
      default: Date.now
    },

    // Thông tin về lần cập nhật cuối
    lastUpdateInfo: {
      // Loại thao tác cuối cùng
      action: {
        type: String,
        enum: ['incoming_added', 'reply_added', 'incoming_removed', 'reply_removed', 'recalculated']
      },
      
      // ID của report liên quan (nếu có)
      reportId: {
        type: Schema.Types.ObjectId,
        ref: 'Report'
      },
      
      // Timestamp của thao tác
      timestamp: {
        type: Number,
        default: Date.now
      }
    },

    // Metadata bổ sung
    metadata: {
      // Ngày khởi tạo counter
      initializedAt: {
        type: Number,
        default: Date.now
      },
      
      // Lần tính toán lại cuối cùng (full recalculation)
      lastRecalculatedAt: {
        type: Number
      },
      
      // Số lần cập nhật counter
      updateCount: {
        type: Number,
        default: 0
      }
    },

    // Soft delete
    deletedAt: {
      type: Number
    },

    // Timestamps
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
);

// Indexes để tối ưu hóa truy vấn
DocumentMetadataSchema.index({ type: 1, unit: 1 }, { unique: true, sparse: true });
DocumentMetadataSchema.index({ type: 1, deletedAt: 1 });
DocumentMetadataSchema.index({ lastUpdated: -1 });

// Pre-save middleware để tự động tính toán unansweredDocuments
DocumentMetadataSchema.pre('save', function(next) {
  if (this.counters) {
    this.counters.unansweredDocuments = Math.max(0, 
      (this.counters.totalIncomingDocuments || 0) - (this.counters.totalRepliedDocuments || 0)
    );
  }
  
  this.updatedAt = Date.now();
  this.metadata.updateCount = (this.metadata.updateCount || 0) + 1;
  
  next();
});

// Static methods để thao tác với counter
DocumentMetadataSchema.statics.incrementIncomingDocuments = async function(unitId = null, count = 1) {
  const filter = { 
    type: 'unanswered_documents_counter',
    deletedAt: { $exists: false }
  };
  
  if (unitId) {
    filter.unit = unitId;
  } else {
    filter.unit = { $exists: false };
  }

  return await this.findOneAndUpdate(
    filter,
    {
      $inc: { 
        'counters.totalIncomingDocuments': count,
        'metadata.updateCount': 1
      },
      $set: {
        'lastUpdated': Date.now(),
        'updatedAt': Date.now(),
        'lastUpdateInfo.action': 'incoming_added',
        'lastUpdateInfo.timestamp': Date.now()
      }
    },
    { 
      upsert: true, 
      new: true,
      setDefaultsOnInsert: true
    }
  );
};

DocumentMetadataSchema.statics.incrementRepliedDocuments = async function(unitId = null, count = 1) {
  const filter = { 
    type: 'unanswered_documents_counter',
    deletedAt: { $exists: false }
  };
  
  if (unitId) {
    filter.unit = unitId;
  } else {
    filter.unit = { $exists: false };
  }

  return await this.findOneAndUpdate(
    filter,
    {
      $inc: { 
        'counters.totalRepliedDocuments': count,
        'metadata.updateCount': 1
      },
      $set: {
        'lastUpdated': Date.now(),
        'updatedAt': Date.now(),
        'lastUpdateInfo.action': 'reply_added',
        'lastUpdateInfo.timestamp': Date.now()
      }
    },
    { 
      upsert: true, 
      new: true,
      setDefaultsOnInsert: true
    }
  );
};

DocumentMetadataSchema.statics.getUnansweredCount = async function(unitId = null) {
  const filter = { 
    type: 'unanswered_documents_counter',
    deletedAt: { $exists: false }
  };
  
  if (unitId) {
    filter.unit = unitId;
  } else {
    filter.unit = { $exists: false };
  }

  const metadata = await this.findOne(filter).lean();
  return metadata ? metadata.counters.unansweredDocuments : 0;
};

module.exports = mongoConnections("master").model("DocumentMetadata", DocumentMetadataSchema);
