const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const DutyMainSchedule = new mongoose.Schema({
  name: {
    type: String,
    required: true // Tên template lịch trực 
  },
  description: {
    type: String // <PERSON>ô tả template
  },
  startTime: {
    type: Number,
    required: true // Ng<PERSON>y bắt đầu áp dụng
  },
  endTime: {
    type: Number,
    required: true // Ngày kết thúc áp dụng
  },
  dutyType: {
    type: String,
    required: true // Loại nhiệm vụ trực
  },
  dutyName: {
    type: String,
    required: true // Tên nhiệm vụ trực
  },
  location: {
    type: String // Địa điểm trực
  },
  weeklyScheduleTemplate: [
    {
      name: String, // Tên của ngày trong tuần
      data: [
        {
          unit: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Unit' // Tham chiếu đến mô hình Unit
          },
          forLeader: {
            type: Boolean,
            default: false // Có phải là lịch trực cho lãnh đạo hay không
          },
          startTime: {
            type: Number,
            required: true // Gi<PERSON> bắt đầu ca (timestamp hoặc số giờ)
          },
          endTime: {
            type: Number,
            required: true // Giờ kết thúc ca
          }
        }
      ]
    }
  ],
  // Lịch trực thực tế
  weeklySchedule: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyShift' // Tham chiếu đến mô hình DutyShift
  }],
  
  status: {
    type: Number,
    default: 1 // 1: active, 0: inactive
  },
  // Thông tin tạo và cập nhật
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('DutyMainSchedule', DutyMainSchedule);
