const _ = require('lodash');
const moment = require('moment');
const StatisticsUtils = require('../utils/statisticsUtils');
const attendanceService = require('./attendanceService');
const CONSTANTS = require('../const');

// Models
const User = require('../models/user');
const DutyShift = require('../models/dutyShift');
const AttendanceRecord = require('../models/attendanceRecord');
const WorkSchedule = require('../models/workSchedule');
const LeaveRequest = require('../models/leaveRequest');
const Area = require('../models/area');
const Report = require('../models/report');

// Duty Schedule Models
const DutyMainSchedule = require('../models/dutyMainSchedule');
const DutySubSchedule = require('../models/dutySubSchedule');
const DutyLocationSchedule = require('../models/dutyLocationSchedule');
const DutyPatrolSchedule = require('../models/dutyPatrolSchedule');
const DutyStadiumSchedule = require('../models/dutyStadiumSchedule');
const DutyCriminalSchedule = require('../models/dutyCriminalSchedule');
const DutySpecializedSchedule = require('../models/dutySpecializedSchedule');

/**
 * Service xử lý logic thống kê cho hệ thống
 * Cung cấp các phương thức tính toán thống kê cho 4 API chính
 */
class StatisticsService {

  /**
   * Lấy danh sách cán bộ đang trực ban
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Danh sách cán bộ trực ban và thống kê
   */
  async getOnDutyOfficers(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      // Tính toán khoảng thời gian
      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);
      const currentTime = Date.now();

      // Lấy tất cả ca trực trong khoảng thời gian
      const dutyShifts = await this.getAllDutyShifts(period);

      // Lọc ra các ca trực đang diễn ra
      const onDutyShifts = dutyShifts.filter(shift => {
        return shift.startTime <= currentTime && shift.endTime >= currentTime && shift.status === 1;
      });

      // Populate thông tin cán bộ
      const populatedShifts = await this.populateOfficerInfo(onDutyShifts);

      // Tính toán thống kê
      const summary = this.calculateOnDutySummary(populatedShifts);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách cán bộ trực ban thành công'
        },
        data: {
          period,
          currentTime,
          officers: populatedShifts,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê số cán bộ điểm danh
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ điểm danh
   */
  async getAttendanceStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu attendance và work schedule
      const [attendanceRecords, workSchedules, dutyShifts] = await Promise.all([
        this.getAttendanceRecords(period),
        this.getWorkSchedules(period),
        this.getAllDutyShifts(period)
      ]);

      // Tính toán thống kê
      const summary = this.calculateAttendanceSummary(attendanceRecords, workSchedules, dutyShifts);
      const byShift = this.calculateAttendanceByShift(attendanceRecords, workSchedules);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ điểm danh thành công'
        },
        data: {
          period,
          summary,
          byShift
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê tổng số lượng cán bộ
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê tổng số lượng cán bộ
   */
  async getOfficerSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy tất cả dữ liệu cần thiết
      const [
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      ] = await Promise.all([
        this.getAllAreas(),
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period),
        this.getApprovedLeaveRequests(period)
      ]);

      // Tính toán thống kê tổng quan
      const summary = this.calculateOfficerSummary(
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      );

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng số lượng cán bộ thành công'
        },
        data: {
          period,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê cán bộ theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ theo khu vực
   */
  async getOfficersByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const [
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      ] = await Promise.all([
        this.getAllAreas(),
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period)
      ]);

      // Tính toán thống kê theo khu vực
      const areaStats = await this.calculateOfficersByArea(
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      );

      const summary = this.calculateAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê số lượng báo cáo theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo khu vực
   */
  async getReportsByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);
      const chartType = 'heatmap';

      // Lấy dữ liệu
      const [
        areas,
        reports,
        allOfficers
      ] = await Promise.all([
        this.getAllAreas(),
        this.getReports(period, chartType),
        this.getAllOfficers()
      ]);

      // Tính toán thống kê báo cáo theo khu vực
      const areaStats = await this.calculateReportsByArea(areas, reports, allOfficers);
      const summary = this.calculateReportAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê tổng quan báo cáo
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê tổng quan báo cáo
   */
  async getReportsSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê tổng quan
      const summary = this.calculateReportsSummary(reports);
      const byStatus = this.calculateReportsByStatus(reports);
      const byReportType = this.calculateReportsByType(reports);
      const byJobType = this.calculateReportsByJobType(reports);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng quan báo cáo thành công'
        },
        data: {
          period,
          summary,
          byStatus,
          byReportType,
          byJobType
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê các vấn đề nổi bật (Incidents Highlight)
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê các vấn đề nổi bật
   */
  async getIncidentsHighlightStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu báo cáo highlight trong khoảng thời gian hiện tại
      const currentReports = await this.getHighlightReports(period);

      // Tính toán thống kê theo lĩnh vực (jobtype)
      const byJobType = this.calculateIncidentsByJobType(currentReports);

      // Tính toán thống kê theo khu vực (area)
      const byArea = this.calculateIncidentsByArea(currentReports);

      // Tính toán tỷ lệ thay đổi so với khoảng thời gian trước (chỉ áp dụng cho non-custom timeRange)
      let changeRate = null;
      if (timeRange !== 'custom') {
        const previousPeriod = StatisticsUtils.getPreviousTimeRange(timeRange, period);
        const previousReports = await this.getHighlightReports(previousPeriod);
        changeRate = this.calculateChangeRate(currentReports, previousReports, byJobType, byArea);
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê các vấn đề nổi bật thành công'
        },
        data: {
          period,
          summary: {
            totalIncidents: this.calculateTotalIncidentsFromReports(currentReports),
            totalReports: currentReports.length
          },
          byJobType: changeRate ? changeRate.byJobType : byJobType,
          byArea: changeRate ? changeRate.byArea : byArea,
          // changeRate
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê các vấn đề khác (Incidents Other)
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê các vấn đề khác
   */
  async getIncidentsOtherStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu báo cáo other trong khoảng thời gian hiện tại
      const currentReports = await this.getOtherReports(period);

      // Tính toán thống kê theo lĩnh vực (jobtype)
      const byJobType = this.calculateIncidentsByJobType(currentReports);

      // Tính toán thống kê theo khu vực (area)
      const byArea = this.calculateIncidentsByArea(currentReports);

      // Tính toán tỷ lệ thay đổi so với khoảng thời gian trước (chỉ áp dụng cho non-custom timeRange)
      let changeRate = null;
      if (timeRange !== 'custom') {
        const previousPeriod = StatisticsUtils.getPreviousTimeRange(timeRange, period);
        const previousReports = await this.getOtherReports(previousPeriod);
        changeRate = this.calculateChangeRate(currentReports, previousReports, byJobType, byArea);
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê các vấn đề khác thành công'
        },
        data: {
          period,
          summary: {
            totalIncidents: this.calculateTotalIncidentsFromReports(currentReports),
            totalReports: currentReports.length
          },
          byJobType: changeRate ? changeRate.byJobType : byJobType,
          // byArea,
          // changeRate
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê báo cáo theo trạng thái chi tiết
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo trạng thái
   */
  async getReportsStatusStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê theo trạng thái
      const statusStats = this.calculateDetailedStatusStats(reports);
      const workflowStats = this.calculateWorkflowStats(reports);
      const processingTime = this.calculateProcessingTimeStats(reports);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo trạng thái thành công'
        },
        data: {
          period,
          statusStats,
          workflowStats,
          processingTime
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê tổng quan văn bản
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'year', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê tổng quan văn bản
   */
  async getDocumentsSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu văn bản trong khoảng thời gian
      const documents = await this.getDocuments(period);

      // Lấy số công văn chưa trả lời từ metadata (toàn thời gian)
      const documentMetadataService = require('./documentMetadataService');
      const unansweredDocuments = await documentMetadataService.getUnansweredDocumentsCount();

      // Tính toán thống kê
      const summary = this.calculateDocumentsSummary(documents, unansweredDocuments);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng quan văn bản thành công'
        },
        data: {
          period,
          summary
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Lấy tất cả ca trực từ 8 loại lịch trực
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách tất cả ca trực
   */
  async getAllDutyShifts(period) {
    const dutyShifts = await DutyShift.find({
      startTime: { $gte: period.startTimestamp },
      endTime: { $lte: period.endTimestamp },
      status: { $ne: 2 } // Không lấy ca đã hủy
    })
      .populate('officer', 'name avatar idNumber units position areas')
      .populate('unit', 'name')
      .lean();

    return dutyShifts;
  }

  /**
   * Populate thông tin chi tiết cán bộ cho ca trực
   * @param {Array} dutyShifts - Danh sách ca trực
   * @returns {Array} Ca trực đã được populate
   */
  async populateOfficerInfo(dutyShifts) {
    return dutyShifts.map(shift => ({
      userId: shift.officer._id,
      name: shift.officer.name,
      avatar: shift.officer.avatar,
      idNumber: shift.officer.idNumber,
      unit: shift.unit,
      position: shift.officer.position,
      dutyInfo: {
        dutyType: this.getDutyType(shift),
        dutyName: shift.name,
        location: shift.locationDuty,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        forLeader: shift.forLeader,
        hasEquipment: shift.hasEquipment
      }
    }));
  }

  /**
   * Xác định loại trực từ ca trực
   * @param {Object} shift - Thông tin ca trực
   * @returns {String} Loại trực
   */
  getDutyType(shift) {
    // Logic xác định loại trực dựa trên source hoặc các field khác
    if (shift.source) {
      return shift.source;
    }

    // Fallback logic
    if (shift.locationDuty) return 'location';
    if (shift.forLeader) return 'main';
    return 'general';
  }

  /**
   * Tính toán thống kê tổng quan cho cán bộ trực ban
   * @param {Array} onDutyShifts - Danh sách ca trực đang diễn ra
   * @returns {Object} Thống kê tổng quan
   */
  calculateOnDutySummary(onDutyShifts) {
    const totalOnDuty = onDutyShifts.length;

    const byDutyType = _.countBy(onDutyShifts, 'dutyInfo.dutyType');
    const byStatus = _.countBy(onDutyShifts, 'dutyInfo.status');

    return {
      totalOnDuty,
      byDutyType,
      byStatus: {
        confirmed: byStatus[1] || 0,
        pending: byStatus[0] || 0,
        cancelled: byStatus[2] || 0
      }
    };
  }

  /**
   * Lấy dữ liệu attendance records
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách attendance records
   */
  async getAttendanceRecords(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await AttendanceRecord.find(dateQuery)
      .populate('user', 'name idNumber units position')
      .lean();
  }

  /**
   * Lấy dữ liệu work schedules
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách work schedules
   */
  async getWorkSchedules(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await WorkSchedule.find({
      ...dateQuery,
      status: 1
    })
      .populate('user', 'name idNumber units position')
      .lean();
  }

  /**
   * Lấy tất cả cán bộ
   * @returns {Array} Danh sách cán bộ
   */
  async getAllOfficers() {
    return await User.find({ status: 1 })
      .populate('units', 'name')
      .populate('position', 'name')
      .populate('areas', 'name level')
      .lean();
  }

  /**
   * Lấy tất cả khu vực
   * @returns {Array} Danh sách khu vực
   */
  async getAllAreas() {
    return await Area.find({ status: 1, level: 1 }) // Chỉ lấy level 1
      .lean();
  }

  /**
   * Lấy đơn xin nghỉ đã được duyệt
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách đơn xin nghỉ
   */
  async getApprovedLeaveRequests(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('startDate', period, 'ddmmyyyy');

    return await LeaveRequest.find({
      ...dateQuery,
      status: 'approved'
    })
      .populate('user', 'name idNumber')
      .lean();
  }

  /**
   * Lấy tất cả báo cáo trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @param {String} chartType - Loại chart: 'pie', 'bar', 'line'
   * @returns {Array} Danh sách báo cáo
   */
  async getReports(period, chartType = '') {
    const query = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    if (chartType) {
      // Lọc các báo cáo không có metrics
      query.quickReportTemplate = {
        chartTypes: chartType
      };
    }

    return await Report.find(query)
      .populate('createdBy', 'name idNumber units areas')
      .populate('jobType', 'name')
      .populate('unit', 'name')
      .populate('details.location.area', 'name level')
      .lean();
  }

  /**
   * Lấy báo cáo highlight trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách báo cáo highlight
   */
  async getHighlightReports(period) {
    const JobType = require('../models/jobType');

    // Lấy tất cả JobType có chartTypes chứa 'highlight'
    const highlightJobTypes = await JobType.find({
      'quickReportTemplate.chartTypes': 'highlight',
      status: 1,
      deletedAt: { $exists: false }
    }).select('_id').lean();

    const highlightJobTypeIds = highlightJobTypes.map(jt => jt._id);

    if (highlightJobTypeIds.length === 0) {
      return [];
    }

    const query = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      jobType: { $in: highlightJobTypeIds },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    return await Report.find(query)
      .populate({
        path: 'createdBy',
        select: 'name idNumber units areas',
        populate: [
          {
            path: 'units',
            select: 'name'
          },
          {
            path: 'areas',
            select: 'name level'
          }
        ]
      })
      .populate('jobType', 'name quickReportTemplate')
      .populate('details.location.areas', 'name level')
      .lean();
  }

  /**
   * Lấy báo cáo khác trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách báo cáo khác
   */
  async getOtherReports(period) {
    const JobType = require('../models/jobType');

    // Lấy tất cả JobType có chartTypes khác 'highlight'
    const otherJobTypes = await JobType.find({
      'quickReportTemplate.chartTypes': { $ne: 'highlight' },
      status: 1,
      deletedAt: { $exists: false }
    }).select('_id').lean();

    const otherJobTypeIds = otherJobTypes.map(jt => jt._id);

    if (otherJobTypeIds.length === 0) {
      return [];
    }

    const query = {
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      jobType: { $in: otherJobTypeIds },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    return await Report.find(query)
      .populate({
        path: 'createdBy',
        select: 'name idNumber units areas',
        populate: [
          {
            path: 'units',
            select: 'name'
          },
          {
            path: 'areas',
            select: 'name level'
          }
        ]
      })
      .populate('jobType', 'name quickReportTemplate')
      .populate('details.location.areas', 'name level')
      .lean();
  }

  /**
   * Tính toán thống kê cán bộ điểm danh tổng quan
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @param {Array} workSchedules - Danh sách work schedules
   * @param {Array} dutyShifts - Danh sách duty shifts
   * @returns {Object} Thống kê tổng quan
   */
  calculateAttendanceSummary(attendanceRecords, workSchedules, dutyShifts) {
    // Tính tổng số ca được lên lịch
    const totalScheduled = _.sumBy(workSchedules, schedule => schedule.shifts.length);

    // Đếm số cán bộ đã điểm danh
    const totalCheckedIn = attendanceRecords.length;

    // Đếm số cán bộ điểm danh muộn
    const totalLate = attendanceRecords.filter(record => record.status === 'late').length;

    // Đếm số cán bộ đúng giờ
    const totalOnTime = attendanceRecords.filter(record => record.status === 'on_time').length;

    // Đếm số cán bộ vắng mặt theo ca
    let totalAbsent = 0;
    workSchedules.forEach(schedule => {
      schedule.shifts.forEach(shift => {
        if (shift.status === 'missed') {
          totalAbsent++;
        }
      });
    });

    // Đếm số đơn xin nghỉ được chấp thuận
    let totalExcused = 0;
    workSchedules.forEach(schedule => {
      schedule.shifts.forEach(shift => {
        if (shift.status === 'excused') {
          totalExcused++;
        }
      });
    });

    // Đếm số cán bộ chưa điểm danh
    const totalNonAttendance = totalScheduled - totalCheckedIn - totalAbsent - totalExcused;

    // Đếm số cán bộ đang trực ban theo shiftsByOfficer và thời gian hiện tại
    const totalOnDuty = this.calculateTotalOfficersOnDuty(dutyShifts);

    // Tính tỷ lệ
    const lateRate = StatisticsUtils.calculateRate(totalLate, totalScheduled);
    const onTimeRate = StatisticsUtils.calculateRate(totalOnTime, totalScheduled);
    const absentRate = StatisticsUtils.calculateRate(totalAbsent, totalScheduled);
    const excusedRate = StatisticsUtils.calculateRate(totalExcused, totalScheduled);
    const nonAttendanceRate = StatisticsUtils.calculateRate(totalNonAttendance, totalScheduled);
    const checkedInRate = StatisticsUtils.calculateRate(totalCheckedIn, totalScheduled);

    return {
      totalScheduled,
      totalCheckedIn,
      totalOnTime,
      totalLate,
      totalExcused,
      totalAbsent,
      totalNonAttendance,
      totalOnDuty,
      lateRate,
      onTimeRate,
      absentRate,
      excusedRate,
      nonAttendanceRate,
      checkedInRate
    };
  }

  /**
   * Tính toán số lượng cán bộ đang trực ban
   * @param {Array} dutyShifts - Danh sách duty shifts
   * @returns {Number} Số lượng cán bộ đang trực ban
   */
  calculateTotalOfficersOnDuty(dutyShifts) {
    const currentTime = Date.now();
    // Nhóm dutyShifts theo officer
    const shiftsByOfficer = {};
    dutyShifts.forEach(dutyShift => {
      const officerId = dutyShift.officer._id.toString();
      if (!shiftsByOfficer[officerId]) {
        shiftsByOfficer[officerId] = [];
      }
      shiftsByOfficer[officerId].push(dutyShift);
    });

    // Đếm số cán bộ đang trực ban theo shiftsByOfficer và thời gian hiện tại
    const totalOnDuty = Object.keys(shiftsByOfficer).filter(officerId => {
      const shifts = shiftsByOfficer[officerId];
      return shifts.some(shift => {
        return shift.startTime <= currentTime && shift.endTime >= currentTime;
      });
    }).length;

    return totalOnDuty;
  }

  /**
   * Tính toán thống kê cán bộ điểm danh theo ca
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @param {Array} workSchedules - Danh sách work schedules
   * @returns {Object} Thống kê theo ca
   */
  calculateAttendanceByShift(attendanceRecords, workSchedules) {
    const result = {};
    ['morning', 'afternoon'].forEach(shift => {
      const scheduled = workSchedules.filter(s => s.shifts.some(item => item.type === shift)).length;
      const checkedIn = attendanceRecords.filter(r => r.shift === shift).length;
      const late = attendanceRecords.filter(r => r.shift === shift && r.status === 'late').length;
      const onTime = attendanceRecords.filter(r => r.shift === shift && r.status === 'on_time').length;
      let absent = 0;
      workSchedules.forEach(schedule => {
        schedule.shifts.forEach(shiftItem => {
          if (shiftItem.type === shift && shiftItem.status === 'missed') {
            absent++;
          }
        });
      });
      let excused = 0;
      workSchedules.forEach(schedule => {
        schedule.shifts.forEach(shiftItem => {
          if (shiftItem.type === shift && shiftItem.status === 'excused') {
            excused++;
          }
        });
      });
      const nonAttendance = scheduled - checkedIn - absent - excused;
      const checkedInRate = StatisticsUtils.calculateRate(checkedIn, scheduled);
      const lateRate = StatisticsUtils.calculateRate(late, scheduled);
      const onTimeRate = StatisticsUtils.calculateRate(onTime, scheduled);
      const absentRate = StatisticsUtils.calculateRate(absent, scheduled);
      const excusedRate = StatisticsUtils.calculateRate(excused, scheduled);
      const nonAttendanceRate = StatisticsUtils.calculateRate(nonAttendance, scheduled);

      result[shift] = {
        scheduled,
        checkedIn,
        late,
        onTime,
        absent,
        excused,
        nonAttendance,
        checkedInRate,
        lateRate,
        onTimeRate,
        absentRate,
        excusedRate,
        nonAttendanceRate
      };
    });

    return result;
  }

  /**
   * Tính toán trạng thái làm việc của cán bộ
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @param {Array} leaveRequests - Đơn xin nghỉ
   * @returns {Object} Thống kê trạng thái làm việc
   */
  calculateOfficerSummary(allOfficers, workSchedules, attendanceRecords, dutyShifts, leaveRequests) {
    const currentTime = Date.now();

    // Tạo map để tra cứu nhanh
    const attendanceMap = _.groupBy(attendanceRecords, 'user._id');
    const scheduleMap = _.groupBy(workSchedules, 'user._id');
    const dutyMap = _.groupBy(dutyShifts.filter(s =>
      s.startTime <= currentTime && s.endTime >= currentTime
    ), 'officer._id');
    const leaveMap = _.groupBy(leaveRequests, 'user._id');

    let working = 0, onDuty = 0, excused = 0, absent = 0, notScheduled = 0;
    const totalOfficers = allOfficers.length;

    allOfficers.forEach(officer => {
      const officerId = officer._id.toString();

      // Kiểm tra nghỉ phép
      if (leaveMap[officerId]) {
        excused++;
        return;
      }

      // Kiểm tra trực ban
      if (dutyMap[officerId]) {
        onDuty++;
        return;
      }

      // Kiểm tra có lịch làm việc không
      const schedules = scheduleMap[officerId];
      if (!schedules || schedules.length === 0) {
        notScheduled++;
        return;
      }

      // Kiểm tra đã điểm danh chưa
      const attendance = attendanceMap[officerId];
      if (attendance && attendance.length > 0) {
        working++;
      } else {
        absent++;
      }
    });

    return {
      totalOfficers,
      working,
      onDuty,
      excused,
      absent,
      notScheduled
    };
  }

  /**
   * Tính toán thống kê cán bộ theo khu vực
   * @param {Array} areas - Danh sách khu vực
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @returns {Array} Thống kê theo khu vực
   */
  async calculateOfficersByArea(areas, allOfficers, workSchedules, attendanceRecords, dutyShifts) {
    const currentTime = Date.now();

    const result = [];

    for (const area of areas) {
      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === area._id.toString())
      );

      const totalOfficers = areaOfficers.length;
      let workingOfficers = 0, onDutyOfficers = 0;

      // Tính toán trạng thái cho từng cán bộ
      areaOfficers.forEach(officer => {
        const officerId = officer._id.toString();

        // Kiểm tra trực ban
        const isOnDuty = dutyShifts.some(shift =>
          shift.officer._id.toString() === officerId &&
          shift.startTime <= currentTime &&
          shift.endTime >= currentTime
        );

        if (isOnDuty) {
          onDutyOfficers++;
        } else {
          // Kiểm tra điểm danh
          const hasAttendance = attendanceRecords.some(record =>
            record.user._id.toString() === officerId
          );

          if (hasAttendance) {
            workingOfficers++;
          }
        }
      });

      // Tính tỷ lệ điểm danh
      const attendanceRate = StatisticsUtils.calculateRate(workingOfficers, totalOfficers);

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalOfficers,
          workingOfficers,
          onDutyOfficers,
          attendanceRate
        }
      });
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho khu vực
   * @param {Array} areaStats - Thống kê theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalOfficers = _.sumBy(areaStats, 'summary.totalOfficers');
    const averageOfficersPerArea = totalAreas > 0 ? Math.round(totalOfficers / totalAreas) : 0;

    // Tìm khu vực có mật độ cán bộ cao nhất
    const highestDensityArea = _.maxBy(areaStats, 'summary.totalOfficers');

    return {
      totalAreas,
      totalOfficers,
      averageOfficersPerArea,
      highestDensityArea: highestDensityArea ? {
        areaId: highestDensityArea.areaId,
        areaName: highestDensityArea.areaName,
        officerCount: highestDensityArea.summary.totalOfficers
      } : null
    };
  }

  /**
   * Tính toán thống kê báo cáo theo khu vực
   * @param {Array} areas - Danh sách khu vực
   * @param {Array} reports - Danh sách báo cáo
   * @param {Array} allOfficers - Danh sách cán bộ
   * @returns {Array} Thống kê báo cáo theo khu vực
   */
  async calculateReportsByArea(areas, reports, allOfficers) {
    const result = [];

    for (const area of areas) {
      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === area._id.toString())
      );

      const areaOfficerIds = areaOfficers.map(officer => officer._id.toString());

      // Lấy báo cáo của khu vực này
      const areaReports = reports.filter(report => {
        // Ưu tiên lấy từ details.location.area nếu có
        if (report.details && report.details.length > 0) {
          return report.details.some(detail =>
            detail.location && detail.location.area &&
            detail.location.area._id.toString() === area._id.toString()
          );
        }

        // Nếu không có location.area, lấy từ createdBy.areas
        return report.createdBy && areaOfficerIds.includes(report.createdBy._id.toString());
      });

      // Tính tổng số vụ việc từ metrics thay vì đếm số báo cáo
      const totalIncidents = this.calculateTotalIncidentsFromReports(areaReports);

      // Thống kê theo loại báo cáo (vẫn đếm số báo cáo)
      const byReportType = _.countBy(areaReports, 'reportType');
      const byStatus = _.countBy(areaReports, 'status');
      const byWorkStatus = _.countBy(areaReports, 'workStatus');

      // Thống kê theo JobType (đếm số vụ việc từ metrics)
      const byJobType = this.calculateIncidentsByJobType(areaReports);

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalIncidents: totalIncidents, // Tổng số vụ việc từ metrics
          totalReports: areaReports.length, // Tổng số báo cáo
          totalOfficers: areaOfficers.length,
          incidentsPerOfficer: StatisticsUtils.calculateRate(totalIncidents, areaOfficers.length),
          reportsPerOfficer: StatisticsUtils.calculateRate(areaReports.length, areaOfficers.length)
        },
        // byReportType: {
        //   quick: byReportType.quick || 0,
        //   detail: byReportType.detail || 0
        // },
        // byStatus: {
        //   submitted: byStatus.submitted || 0,
        //   approved: byStatus.approved || 0,
        //   rejected: byStatus.rejected || 0
        // },
        // byWorkStatus: {
        //   pending: byWorkStatus.pending || 0,
        //   in_progress: byWorkStatus.in_progress || 0,
        //   completed: byWorkStatus.completed || 0,
        //   cancelled: byWorkStatus.cancelled || 0,
        //   on_hold: byWorkStatus.on_hold || 0
        // },
        // byJobType,
        // reports: areaReports.map(report => ({
        //   id: report._id,
        //   title: report.title,
        //   reportType: report.reportType,
        //   status: report.status,
        //   workStatus: report.workStatus,
        //   jobType: report.jobType?.name,
        //   createdBy: report.createdBy?.name,
        //   createdAt: report.createdAt,
        //   incidents: this.calculateTotalIncidentsFromReports([report]) // Số vụ việc trong báo cáo này
        // }))
      });
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho báo cáo theo khu vực
   * @param {Array} areaStats - Thống kê báo cáo theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateReportAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalIncidents = areaStats.reduce((sum, area) => sum + area.summary.totalIncidents, 0);
    const totalReports = areaStats.reduce((sum, area) => sum + area.summary.totalReports, 0);
    const totalOfficers = areaStats.reduce((sum, area) => sum + area.summary.totalOfficers, 0);

    const averageIncidentsPerArea = StatisticsUtils.calculateRate(totalIncidents, totalAreas);
    const averageIncidentsPerOfficer = StatisticsUtils.calculateRate(totalIncidents, totalOfficers);
    const averageReportsPerArea = StatisticsUtils.calculateRate(totalReports, totalAreas);
    const averageReportsPerOfficer = StatisticsUtils.calculateRate(totalReports, totalOfficers);

    // Tìm khu vực có nhiều vụ việc nhất
    const maxIncidentsArea = areaStats.reduce((max, area) =>
      area.summary.totalIncidents > max.totalIncidents ?
        { areaName: area.areaName, totalIncidents: area.summary.totalIncidents } : max,
      { areaName: '', totalIncidents: 0 }
    );

    return {
      totalAreas,
      totalIncidents, // Tổng số vụ việc (từ metrics)
      totalReports,   // Tổng số báo cáo
      totalOfficers,
      averageIncidentsPerArea,
      averageIncidentsPerOfficer,
      averageReportsPerArea,
      averageReportsPerOfficer,
      maxIncidentsArea
    };
  }

  /**
   * Tính toán thống kê tổng quan báo cáo
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê tổng quan
   */
  calculateReportsSummary(reports) {
    const totalReports = reports.length;
    const totalIncidents = this.calculateTotalIncidentsFromReports(reports);

    const quickReports = reports.filter(r => r.reportType === 'quick').length;
    const detailReports = reports.filter(r => r.reportType === 'detail').length;

    // Tính số vụ việc theo loại báo cáo
    const quickIncidents = this.calculateTotalIncidentsFromReports(
      reports.filter(r => r.reportType === 'quick')
    );
    const detailIncidents = this.calculateTotalIncidentsFromReports(
      reports.filter(r => r.reportType === 'detail')
    );

    const submittedReports = reports.filter(r => r.status === 'submitted').length;
    const approvedReports = reports.filter(r => r.status === 'approved').length;
    const rejectedReports = reports.filter(r => r.status === 'rejected').length;

    const completedReports = reports.filter(r => r.workStatus === 'completed').length;
    const inProgressReports = reports.filter(r => r.workStatus === 'in_progress').length;
    const pendingReports = reports.filter(r => r.workStatus === 'pending').length;

    return {
      totalReports,
      totalIncidents, // Tổng số vụ việc từ metrics
      quickReports,
      detailReports,
      quickIncidents, // Số vụ việc từ báo cáo nhanh
      detailIncidents, // Số vụ việc từ báo cáo chi tiết
      submittedReports,
      approvedReports,
      rejectedReports,
      completedReports,
      inProgressReports,
      pendingReports,
      quickReportRate: StatisticsUtils.calculateRate(quickReports, totalReports),
      quickIncidentRate: StatisticsUtils.calculateRate(quickIncidents, totalIncidents),
      approvalRate: StatisticsUtils.calculateRate(approvedReports, totalReports),
      completionRate: StatisticsUtils.calculateRate(completedReports, totalReports),
      incidentsPerReport: StatisticsUtils.calculateRate(totalIncidents, totalReports)
    };
  }

  /**
   * Tính toán thống kê báo cáo theo trạng thái
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê theo trạng thái
   */
  calculateReportsByStatus(reports) {
    const byStatus = _.countBy(reports, 'status');
    const byWorkStatus = _.countBy(reports, 'workStatus');

    return {
      status: {
        draft: byStatus.draft || 0,
        submitted: byStatus.submitted || 0,
        approved: byStatus.approved || 0,
        rejected: byStatus.rejected || 0
      },
      workStatus: {
        pending: byWorkStatus.pending || 0,
        in_progress: byWorkStatus.in_progress || 0,
        completed: byWorkStatus.completed || 0,
        cancelled: byWorkStatus.cancelled || 0,
        on_hold: byWorkStatus.on_hold || 0
      }
    };
  }

  /**
   * Tính toán thống kê báo cáo theo loại
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê theo loại báo cáo
   */
  calculateReportsByType(reports) {
    const byType = _.countBy(reports, 'reportType');
    const total = reports.length;

    return {
      quick: {
        count: byType.quick || 0,
        percentage: StatisticsUtils.calculateRate(byType.quick || 0, total)
      },
      detail: {
        count: byType.detail || 0,
        percentage: StatisticsUtils.calculateRate(byType.detail || 0, total)
      }
    };
  }

  /**
   * Tính toán thống kê báo cáo theo loại công việc
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Array} Thống kê theo JobType
   */
  calculateReportsByJobType(reports) {
    const byJobType = {};
    const total = reports.length;

    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeName = report.jobType.name || 'Không xác định';
        const jobTypeId = report.jobType._id || 'unknown';

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            count: 0
          };
        }
        byJobType[jobTypeId].count++;
      }
    });

    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculateRate(item.count, total)
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Tính tổng số vụ việc từ metrics của các báo cáo
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Number} Tổng số vụ việc
   */
  calculateTotalIncidentsFromReports(reports) {
    let totalIncidents = 0;

    reports.forEach(report => {
      if (report.metrics && typeof report.metrics === 'object') {
        // Tính tổng tất cả các giá trị số trong metrics
        Object.keys(report.metrics).forEach(key => {
          if (report.jobType && report.jobType.quickReportTemplate && report.jobType.quickReportTemplate.metrics && report.jobType.quickReportTemplate.metrics[key] && report.jobType.quickReportTemplate.metrics[key].needsDetails) {
            const value = report.metrics[key]
            if (typeof value === 'number' && value > 0) {
              totalIncidents += value;
            }
          }
        })
      }
    });

    return totalIncidents;
  }

  /**
   * Tính số vụ việc theo JobType từ metrics
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Array} Thống kê số vụ việc theo JobType
   */
  calculateIncidentsByJobType(reports) {
    const byJobType = {};
    const totalIncidents = this.calculateTotalIncidentsFromReports(reports);

    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeId = report.jobType._id.toString();
        const jobTypeName = report.jobType.name || 'Không xác định';
        const incidents = this.calculateTotalIncidentsFromReports([report]);

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            totalReports: 0,
            totalIncidents: 0
          };
        }

        byJobType[jobTypeId].totalReports++;
        byJobType[jobTypeId].totalIncidents += incidents;
      }
    });

    // Chuyển đổi thành array và tính phần trăm
    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents)
    })).sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính toán thống kê vụ việc theo khu vực (Area)
   * @param {Array} reports - Danh sách báo cáo highlight
   * @returns {Array} Thống kê theo Area
   */
  calculateIncidentsByArea(reports) {
    const byArea = {};
    const totalIncidents = this.calculateTotalIncidentsFromReports(reports);

    reports.forEach(report => {
      // Chỉ lấy thông tin khu vực từ details.location.areas
      let areas = [];
      if (report.details && report.details.length > 0) {
        report.details.forEach(detail => {
          if (detail.location && detail.location.areas && detail.location.areas.length > 0) {
            areas.push(...detail.location.areas);
          }
        });
      }

      // Lọc ra các area level 1 (không tính các area level 2)
      areas = areas.filter(area => typeof area === 'object' && area.level === 1);

      const reportIncidents = this.calculateTotalIncidentsFromReports([report]);

      if (areas.length > 0) {
        // Đếm số lần xuất hiện của mỗi khu vực
        const areaCount = {};
        areas.forEach(area => {
          const areaId = area._id ? area._id.toString() : area.toString();
          const areaName = area.name || 'Không xác định';

          if (!areaCount[areaId]) {
            areaCount[areaId] = {
              areaId,
              areaName,
              count: 0
            };
          }
          areaCount[areaId].count++;
        });

        // Tính tổng số lần xuất hiện của tất cả khu vực
        const totalAreaOccurrences = Object.values(areaCount).reduce((sum, item) => sum + item.count, 0);

        // Phân bổ vụ việc theo logic mới
        let assignedIncidents = 0;

        if (reportIncidents >= totalAreaOccurrences) {
          // Trường hợp số vụ việc >= số lần xuất hiện khu vực
          Object.values(areaCount).forEach(areaInfo => {
            const { areaId, areaName, count } = areaInfo;

            if (!byArea[areaId]) {
              byArea[areaId] = {
                areaId,
                areaName,
                totalReports: 0,
                totalIncidents: 0
              };
            }

            byArea[areaId].totalReports++;
            byArea[areaId].totalIncidents += count;
            assignedIncidents += count;
          });

          // Vụ việc còn lại gán vào nhóm "Không xác định"
          const remainingIncidents = reportIncidents - assignedIncidents;
          if (remainingIncidents > 0) {
            if (!byArea['unassigned']) {
              byArea['unassigned'] = {
                areaId: 'unassigned',
                areaName: 'Không xác định',
                totalReports: 0,
                totalIncidents: 0
              };
            }
            byArea['unassigned'].totalReports++;
            byArea['unassigned'].totalIncidents += remainingIncidents;
          }
        } else {
          // Trường hợp số vụ việc < số lần xuất hiện khu vực
          // Cắt length của areas bằng đúng số vụ việc rồi phân bổ
          const limitedAreas = areas.slice(0, reportIncidents);

          limitedAreas.forEach(area => {
            const areaId = area._id ? area._id.toString() : area.toString();
            const areaName = area.name || 'Không xác định';

            if (!byArea[areaId]) {
              byArea[areaId] = {
                areaId,
                areaName,
                totalReports: 0,
                totalIncidents: 0
              };
            }

            byArea[areaId].totalReports++;
            byArea[areaId].totalIncidents += 1; // Mỗi khu vực được gán 1 vụ việc
          });
        }
      } else {
        // Nếu không có thông tin khu vực, gán tất cả vào nhóm 'unassigned'
        if (!byArea['unassigned']) {
          byArea['unassigned'] = {
            areaId: 'unassigned',
            areaName: 'Không xác định',
            totalReports: 0,
            totalIncidents: 0
          };
        }
        byArea['unassigned'].totalReports++;
        byArea['unassigned'].totalIncidents += reportIncidents;
      }
    });

    // Chuyển đổi thành array và tính phần trăm
    return Object.values(byArea).map(item => ({
      ...item,
      totalIncidents: Math.round(item.totalIncidents), // Đảm bảo là số nguyên
      percentage: StatisticsUtils.calculatePercentage(item.totalIncidents, totalIncidents)
    })).sort((a, b) => b.totalIncidents - a.totalIncidents);
  }

  /**
   * Tính toán tỷ lệ thay đổi so với khoảng thời gian trước
   * @param {Array} currentReports - Báo cáo khoảng thời gian hiện tại
   * @param {Array} previousReports - Báo cáo khoảng thời gian trước
   * @param {Array} currentByJobType - Thống kê hiện tại theo JobType
   * @param {Array} currentByArea - Thống kê hiện tại theo Area
   * @returns {Object} Tỷ lệ thay đổi
   */
  calculateChangeRate(currentReports, previousReports, currentByJobType, currentByArea) {
    const currentTotal = this.calculateTotalIncidentsFromReports(currentReports);
    const previousTotal = this.calculateTotalIncidentsFromReports(previousReports);

    // Tính tỷ lệ thay đổi tổng thể
    const totalChangeRate = previousTotal > 0 ?
      Math.round(((currentTotal - previousTotal) / previousTotal) * 100) :
      (currentTotal > 0 ? 100 : 0);

    // Tính tỷ lệ thay đổi theo JobType
    const previousByJobType = this.calculateIncidentsByJobType(previousReports);
    const jobTypeChanges = currentByJobType.map(current => {
      const previous = previousByJobType.find(p => p.jobTypeId === current.jobTypeId);
      const previousIncidents = previous ? previous.totalIncidents : 0;
      const changeRate = previousIncidents > 0 ?
        Math.round(((current.totalIncidents - previousIncidents) / previousIncidents) * 100) :
        (current.totalIncidents > 0 ? 100 : 0);

      return {
        ...current,
        previousIncidents,
        changeRate,
        changeType: changeRate > 0 ? 'increase' : changeRate < 0 ? 'decrease' : 'stable'
      };
    });

    // Tính tỷ lệ thay đổi theo Area
    const previousByArea = this.calculateIncidentsByArea(previousReports);
    const areaChanges = currentByArea.map(current => {
      const previous = previousByArea.find(p => p.areaId === current.areaId);
      const previousIncidents = previous ? previous.totalIncidents : 0;
      const changeRate = previousIncidents > 0 ?
        Math.round(((current.totalIncidents - previousIncidents) / previousIncidents) * 100) :
        (current.totalIncidents > 0 ? 100 : 0);

      return {
        ...current,
        previousIncidents,
        changeRate,
        changeType: changeRate > 0 ? 'increase' : changeRate < 0 ? 'decrease' : 'stable'
      };
    });

    return {
      total: {
        current: currentTotal,
        previous: previousTotal,
        changeRate: totalChangeRate,
        changeType: totalChangeRate > 0 ? 'increase' : totalChangeRate < 0 ? 'decrease' : 'stable'
      },
      byJobType: jobTypeChanges,
      byArea: areaChanges
    };
  }

  /**
   * Tính toán thống kê trạng thái chi tiết
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê trạng thái chi tiết
   */
  calculateDetailedStatusStats(reports) {
    const total = reports.length;
    const byStatus = _.countBy(reports, 'status');
    const byWorkStatus = _.countBy(reports, 'workStatus');
    const byReportType = _.countBy(reports, 'reportType');

    // Thống kê theo status
    const statusBreakdown = {
      draft: {
        count: byStatus.draft || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.draft || 0, total)
      },
      submitted: {
        count: byStatus.submitted || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.submitted || 0, total)
      },
      approved: {
        count: byStatus.approved || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.approved || 0, total)
      },
      rejected: {
        count: byStatus.rejected || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.rejected || 0, total)
      }
    };

    // Thống kê theo workStatus
    const workStatusBreakdown = {
      pending: {
        count: byWorkStatus.pending || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.pending || 0, total)
      },
      in_progress: {
        count: byWorkStatus.in_progress || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.in_progress || 0, total)
      },
      completed: {
        count: byWorkStatus.completed || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.completed || 0, total)
      },
      cancelled: {
        count: byWorkStatus.cancelled || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.cancelled || 0, total)
      },
      on_hold: {
        count: byWorkStatus.on_hold || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.on_hold || 0, total)
      }
    };

    return {
      total,
      statusBreakdown,
      workStatusBreakdown,
      reportTypeBreakdown: {
        quick: {
          count: byReportType.quick || 0,
          percentage: StatisticsUtils.calculateRate(byReportType.quick || 0, total)
        },
        detail: {
          count: byReportType.detail || 0,
          percentage: StatisticsUtils.calculateRate(byReportType.detail || 0, total)
        }
      }
    };
  }

  /**
   * Tính toán thống kê workflow
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê workflow
   */
  calculateWorkflowStats(reports) {
    const total = reports.length;

    // Báo cáo đã hoàn thành workflow (approved hoặc rejected)
    const completedWorkflow = reports.filter(r =>
      r.status === 'approved' || r.status === 'rejected'
    ).length;

    // Báo cáo đang chờ xử lý
    const pendingWorkflow = reports.filter(r => r.status === 'submitted').length;

    // Báo cáo nháp
    const draftReports = reports.filter(r => r.status === 'draft').length;

    // Tỷ lệ approval
    const approvedReports = reports.filter(r => r.status === 'approved').length;
    const submittedReports = reports.filter(r => r.status !== 'draft').length;
    const approvalRate = StatisticsUtils.calculateRate(approvedReports, submittedReports);

    // Tỷ lệ completion (work status)
    const completedWork = reports.filter(r => r.workStatus === 'completed').length;
    const completionRate = StatisticsUtils.calculateRate(completedWork, total);

    return {
      total,
      completedWorkflow,
      pendingWorkflow,
      draftReports,
      approvalRate,
      completionRate,
      workflowEfficiency: StatisticsUtils.calculateRate(completedWorkflow, total)
    };
  }

  /**
   * Tính toán thống kê thời gian xử lý
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê thời gian xử lý
   */
  calculateProcessingTimeStats(reports) {
    const processedReports = reports.filter(r =>
      r.status === 'approved' || r.status === 'rejected'
    );

    if (processedReports.length === 0) {
      return {
        averageProcessingTime: 0,
        fastestProcessing: 0,
        slowestProcessing: 0,
        processedCount: 0
      };
    }

    // Tính thời gian xử lý (giả sử có updatedAt)
    const processingTimes = processedReports.map(report => {
      // Thời gian từ khi tạo đến khi được approve/reject
      return report.updatedAt - report.createdAt;
    }).filter(time => time > 0);

    if (processingTimes.length === 0) {
      return {
        averageProcessingTime: 0,
        fastestProcessing: 0,
        slowestProcessing: 0,
        processedCount: processedReports.length
      };
    }

    const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
    const fastestProcessing = Math.min(...processingTimes);
    const slowestProcessing = Math.max(...processingTimes);

    // Convert từ milliseconds sang hours
    return {
      averageProcessingTime: Math.round(averageProcessingTime / (1000 * 60 * 60) * 100) / 100,
      fastestProcessing: Math.round(fastestProcessing / (1000 * 60 * 60) * 100) / 100,
      slowestProcessing: Math.round(slowestProcessing / (1000 * 60 * 60) * 100) / 100,
      processedCount: processedReports.length
    };
  }

  /**
   * Lấy tất cả văn bản trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách văn bản
   */
  async getDocuments(period) {
    return await Report.find({
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      jobType: CONSTANTS.DOCUMENT_JOB_TYPE_ID // Chỉ lấy reports có tính chất văn bản
    })
      .populate('createdBy', 'name idNumber units')
      .populate('unit', 'name')
      .lean();
  }

  /**
   * Tính toán thống kê tổng quan văn bản
   * @param {Array} documents - Danh sách văn bản
   * @param {Number} unansweredDocuments - Số công văn chưa trả lời (từ metadata)
   * @returns {Object} Thống kê tổng quan
   */
  calculateDocumentsSummary(documents, unansweredDocuments = 0) {
    const totalReports = documents.length;

    const incomingDocs = documents.map(d => d.metrics?.incomingDocuments || 0).reduce((sum, count) => sum + count, 0);
    const outgoingDocs = documents.map(d => d.metrics?.outgoingDocuments || 0).reduce((sum, count) => sum + count, 0);
    const replyDocs = documents.map(d => d.metrics?.repliedDocuments || 0).reduce((sum, count) => sum + count, 0);
    const totalDocuments = incomingDocs + outgoingDocs + replyDocs;

    return {
      totalReports,
      totalDocuments,
      incomingDocuments: incomingDocs,
      outgoingDocuments: outgoingDocs,
      replyDocuments: replyDocs,
      unansweredDocuments: unansweredDocuments, // Số công văn chưa trả lời từ metadata
      incomingRate: StatisticsUtils.calculateRate(incomingDocs, totalDocuments),
      outgoingRate: StatisticsUtils.calculateRate(outgoingDocs, totalDocuments),
      replyRate: StatisticsUtils.calculateRate(replyDocs, totalDocuments),
      unansweredRate: StatisticsUtils.calculateRate(unansweredDocuments, incomingDocs) // Tỷ lệ chưa trả lời
    };
  }
}

module.exports = new StatisticsService();
