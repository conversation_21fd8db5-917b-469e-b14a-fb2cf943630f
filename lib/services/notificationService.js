/**
 * Service xử lý thông báo và cảnh báo
 * Quản lý lên lịch và gửi thông báo tự động cho hệ thống điểm danh
 */

const _ = require('lodash');
const moment = require('moment');
const NotificationSchedule = require('../models/notificationSchedule');
const WorkSchedule = require('../models/workSchedule');
const User = require('../models/user');
const NotifyManager = require('../jobs/pushNotify');
const MailUtil = require('../util/mail');

class NotificationService {
  /**
   * Lên lịch thông báo cho cán bộ
   * @param {String} userId - ID cán bộ
   * @param {Array} workSchedules - Danh sách lịch làm việc
   * @returns {Object} Kết quả lên lịch
   */
  async scheduleNotifications(userId, workSchedules) {
    try {
      const notifications = [];

      for (const schedule of workSchedules) {
        for (const shift of schedule.shifts) {
          // Parse ngày với định dạng DD-MM-YYYY
          const scheduleDate = moment(schedule.date, 'DD-MM-YYYY');
          const shiftStartTime = moment(`${schedule.date} ${shift.startTime}`, 'DD-MM-YYYY HH:mm');

          // Thông báo 30 phút trước
          const reminder30min = shiftStartTime.clone().subtract(30, 'minutes');
          if (reminder30min.isAfter(moment())) {
            notifications.push({
              user: userId,
              type: 'reminder_30min',
              scheduleTime: reminder30min.toDate(),
              message: `Nhắc nhở: Còn 30 phút nữa đến giờ làm việc ca ${shift.type === 'morning' ? 'sáng' : 'chiều'} (${shift.startTime})`,
              data: {
                scheduleId: schedule._id,
                shift: shift.type,
                startTime: shift.startTime,
                date: schedule.date
              }
            });
          }

          // Thông báo 5 phút trước
          const reminder5min = shiftStartTime.clone().subtract(5, 'minutes');
          if (reminder5min.isAfter(moment())) {
            notifications.push({
              user: userId,
              type: 'reminder_5min',
              scheduleTime: reminder5min.toDate(),
              message: `Cảnh báo: Còn 5 phút nữa đến giờ làm việc ca ${shift.type === 'morning' ? 'sáng' : 'chiều'} (${shift.startTime})`,
              data: {
                scheduleId: schedule._id,
                shift: shift.type,
                startTime: shift.startTime,
                date: schedule.date
              }
            });
          }

          // Thông báo đến giờ làm việc
          if (shiftStartTime.isAfter(moment())) {
            notifications.push({
              user: userId,
              type: 'work_time',
              scheduleTime: shiftStartTime.toDate(),
              message: `Đã đến giờ làm việc ca ${shift.type === 'morning' ? 'sáng' : 'chiều'}. Vui lòng điểm danh!`,
              data: {
                scheduleId: schedule._id,
                shift: shift.type,
                startTime: shift.startTime,
                date: schedule.date
              }
            });
          }
        }
      }

      // Xóa thông báo cũ chưa gửi
      await NotificationSchedule.deleteMany({
        user: userId,
        status: 'pending',
        scheduleTime: { $gte: new Date() }
      });

      // Tạo thông báo mới
      if (notifications.length > 0) {
        await NotificationSchedule.insertMany(notifications);
      }

      return {
        success: true,
        message: `Đã lên lịch ${notifications.length} thông báo`,
        data: { count: notifications.length }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Gửi thông báo push notification
   * @param {String} userId - ID cán bộ
   * @param {String} title - Tiêu đề thông báo
   * @param {String} message - Nội dung thông báo
   * @param {Object} data - Dữ liệu bổ sung
   * @returns {Object} Kết quả gửi thông báo
   */
  async sendPushNotification(userId, title, message, data = {}) {
    try {
      await NotifyManager.sendToMember(
        userId,
        title,
        message,
        data,
        'attendance_notification',
        'ioc'
      );

      return {
        success: true,
        message: 'Gửi push notification thành công'
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Gửi email thông báo
   * @param {String} userId - ID cán bộ
   * @param {String} subject - Tiêu đề email
   * @param {String} content - Nội dung email
   * @returns {Object} Kết quả gửi email
   */
  async sendEmailNotification(userId, subject, content) {
    try {
      // Lấy thông tin user để có email
      const user = await User.findById(userId).select('email name').lean();

      if (!user || !user.email) {
        return {
          success: false,
          message: 'Không tìm thấy email của cán bộ'
        };
      }

      const emailBody = {
        subject: `[IOC Hồng Bàng] ${subject}`,
        text: content,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2c3e50;">IOC Hồng Bàng - Hệ thống điểm danh</h2>
            <p>Xin chào <strong>${user.name}</strong>,</p>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
              ${content.replace(/\n/g, '<br>')}
            </div>
            <p style="color: #6c757d; font-size: 12px;">
              Đây là email tự động từ hệ thống. Vui lòng không trả lời email này.
            </p>
          </div>
        `
      };

      MailUtil.sendEMail(emailBody, user.email);

      return {
        success: true,
        message: 'Gửi email thông báo thành công'
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Xử lý thông báo định kỳ (chạy bởi cron job)
   * @returns {Object} Kết quả xử lý
   */
  async processScheduledNotifications() {
    try {
      const now = new Date();
      const fiveMinutesAgo = moment().subtract(5, 'minutes').toDate();

      // Lấy thông báo cần gửi (trong khoảng 5 phút qua đến hiện tại)
      const pendingNotifications = await NotificationSchedule.find({
        status: 'pending',
        scheduleTime: {
          $gte: fiveMinutesAgo,
          $lte: now
        }
      }).populate('user', 'name email').lean();

      let sentCount = 0;
      let failedCount = 0;

      for (const notification of pendingNotifications) {
        try {
          // Gửi push notification
          const pushResult = await this.sendPushNotification(
            notification.user._id,
            'Thông báo điểm danh',
            notification.message,
            notification.data
          );

          // Gửi email (optional, chỉ cho thông báo quan trọng)
          if (notification.type === 'work_time') {
            await this.sendEmailNotification(
              notification.user._id,
              'Thông báo điểm danh',
              notification.message
            );
          }

          // Cập nhật trạng thái thành công
          await NotificationSchedule.findByIdAndUpdate(notification._id, {
            status: 'sent',
            sentAt: new Date()
          });

          sentCount++;

        } catch (error) {
          // Cập nhật trạng thái thất bại
          await NotificationSchedule.findByIdAndUpdate(notification._id, {
            status: 'failed',
            error: error.message,
            retryCount: (notification.retryCount || 0) + 1
          });

          failedCount++;
          logger.logError(['Notification send failed:', error], 'processScheduledNotifications');
        }
      }

      // Cleanup thông báo cũ (quá 7 ngày)
      const sevenDaysAgo = moment().subtract(7, 'days').toDate();
      await NotificationSchedule.deleteMany({
        createdAt: { $lt: sevenDaysAgo.getTime() }
      });

      return {
        success: true,
        message: `Xử lý thông báo hoàn tất: ${sentCount} thành công, ${failedCount} thất bại`,
        data: {
          sent: sentCount,
          failed: failedCount,
          total: pendingNotifications.length
        }
      };

    } catch (error) {
      logger.logError(['Process scheduled notifications error:', error]);
      return {
        success: false
      };
    }
  }

  /**
   * Gửi thông báo khi đơn xin nghỉ được duyệt
   * @param {Object} leaveRequest - Đơn xin nghỉ
   * @returns {Object} Kết quả gửi thông báo
   */
  async sendLeaveRequestNotification(leaveRequest) {
    try {
      const statusText = leaveRequest.status === 'approved' ? 'được duyệt' : 'bị từ chối';
      const title = `Đơn xin nghỉ ${statusText}`;
      const message = `Đơn xin nghỉ từ ${leaveRequest.startDate} đến ${leaveRequest.endDate || leaveRequest.startDate} đã ${statusText}.`;

      // Gửi push notification
      await this.sendPushNotification(
        leaveRequest.user,
        title,
        message,
        {
          requestId: leaveRequest._id,
          status: leaveRequest.status,
          type: 'leave_request_update'
        }
      );

      // Gửi email
      await this.sendEmailNotification(
        leaveRequest.user,
        title,
        `${message}\n\nLý do: ${leaveRequest.reason}\n${leaveRequest.approvalNote ? `Ghi chú: ${leaveRequest.approvalNote}` : ''}`
      );

      return {
        success: true,
        message: 'Gửi thông báo đơn xin nghỉ thành công'
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tự động lên lịch thông báo khi tạo lịch làm việc mới
   * @param {Array} scheduleIds - Danh sách ID lịch làm việc
   * @returns {Object} Kết quả lên lịch
   */
  async autoScheduleForNewWorkSchedules(scheduleIds) {
    try {
      const schedules = await WorkSchedule.find({
        _id: { $in: scheduleIds },
        status: 1
      }).lean();

      // Nhóm lịch theo user
      const schedulesByUser = _.groupBy(schedules, 'user');
      let totalNotifications = 0;

      for (const [userId, userSchedules] of Object.entries(schedulesByUser)) {
        const result = await this.scheduleNotifications(userId, userSchedules);
        if (result.success) {
          totalNotifications += result.data.count;
        }
      }

      return {
        success: true,
        message: `Đã tự động lên lịch ${totalNotifications} thông báo cho ${Object.keys(schedulesByUser).length} cán bộ`,
        data: { totalNotifications }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy thống kê thông báo
   * @param {String} userId - ID cán bộ (optional)
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Thống kê thông báo
   */
  async getNotificationStatistics(userId = null, startDate, endDate) {
    try {
      const query = {};

      if (userId) {
        query.user = userId;
      }

      if (startDate && endDate) {
        query.createdAt = {
          $gte: moment(startDate).valueOf(),
          $lte: moment(endDate).valueOf()
        };
      }

      const [statusStats, typeStats, total] = await Promise.all([
        NotificationSchedule.aggregate([
          { $match: query },
          { $group: { _id: '$status', count: { $sum: 1 } } }
        ]),
        NotificationSchedule.aggregate([
          { $match: query },
          { $group: { _id: '$type', count: { $sum: 1 } } }
        ]),
        NotificationSchedule.countDocuments(query)
      ]);

      const statistics = {
        total,
        byStatus: this.formatAggregationResult(statusStats),
        byType: this.formatAggregationResult(typeStats),
        period: { startDate, endDate }
      };

      return {
        success: true,
        message: 'Lấy thống kê thông báo thành công',
        data: statistics
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Format kết quả aggregation
   * @param {Array} results - Kết quả aggregation
   * @returns {Object} Kết quả đã format
   */
  formatAggregationResult(results) {
    const formatted = {};
    results.forEach(item => {
      formatted[item._id] = item.count;
    });
    return formatted;
  }
}

module.exports = new NotificationService();