const DocumentMetadata = require('../models/documentMetadata');
const Report = require('../models/report');
const CONSTANTS = require('../const');

/**
 * Service quản lý metadata và counter cho hệ thống công văn
 * Tối ưu hóa việc tính toán thống kê công văn chưa trả lời
 */
class DocumentMetadataService {
  constructor() {
    this.DOCUMENT_JOB_TYPE_ID = CONSTANTS.DOCUMENT_JOB_TYPE_ID;
  }

  /**
   * Khởi tạo hoặc tính toán lại counter từ đầu
   * Sử dụng khi lần đầu triển khai hoặc cần sync lại dữ liệu
   * @param {String} unitId - ID đơn vị (optional)
   * @returns {Object} Kết quả khởi tạo
   */
  async initializeCounter(unitId = null) {
    try {
      console.log(`[DocumentMetadataService] Initializing counter for unit: ${unitId || 'all'}`);

      // Tính toán từ dữ liệu hiện có
      const result = await this.recalculateFromDatabase(unitId);

      console.log(`[DocumentMetadataService] Counter initialized successfully:`, result);
      return {
        success: true,
        message: 'Khởi tạo counter thành công',
        data: result
      };

    } catch (error) {
      console.error('[DocumentMetadataService] Error initializing counter:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Tính toán lại counter từ database
   * @param {String} unitId - ID đơn vị (optional)
   * @returns {Object} Kết quả tính toán
   */
  async recalculateFromDatabase(unitId = null) {
    try {
      // Query để lấy tất cả reports công văn
      const query = {
        jobType: this.DOCUMENT_JOB_TYPE_ID,
        deletedAt: { $exists: false }
      };

      if (unitId) {
        query.unit = unitId;
      }

      // Lấy tất cả reports công văn
      const reports = await Report.find(query).lean();

      // Tính toán tổng số công văn đến và đã trả lời
      let totalIncoming = 0;
      let totalReplied = 0;

      reports.forEach(report => {
        if (report.metrics) {
          totalIncoming += report.metrics.incomingDocuments || 0;
          totalReplied += report.metrics.repliedDocuments || 0;
        }
      });

      // Cập nhật hoặc tạo mới metadata
      const filter = {
        type: 'unanswered_documents_counter',
        deletedAt: { $exists: false }
      };

      if (unitId) {
        filter.unit = unitId;
      } else {
        filter.unit = { $exists: false };
      }

      const updateData = {
        $set: {
          'counters.totalIncomingDocuments': totalIncoming,
          'counters.totalRepliedDocuments': totalReplied,
          'counters.unansweredDocuments': Math.max(0, totalIncoming - totalReplied),
          'lastUpdated': Date.now(),
          'updatedAt': Date.now(),
          'lastUpdateInfo.action': 'recalculated',
          'lastUpdateInfo.timestamp': Date.now(),
          'metadata.lastRecalculatedAt': Date.now()
        },
        $inc: {
          'metadata.updateCount': 1
        }
      };

      const metadata = await DocumentMetadata.findOneAndUpdate(
        filter,
        updateData,
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      );

      console.log(`[DocumentMetadataService] Metadata recalculated successfully:`, metadata);

      return {
        unitId: unitId,
        totalIncomingDocuments: totalIncoming,
        totalRepliedDocuments: totalReplied,
        unansweredDocuments: totalIncoming - totalReplied,
        reportsProcessed: reports.length,
        lastRecalculatedAt: Date.now()
      };

    } catch (error) {
      console.error('[DocumentMetadataService] Error recalculating from database:', error);
      throw error;
    }
  }

  /**
   * Cập nhật counter khi có công văn đến mới
   * @param {Object} reportData - Dữ liệu report
   * @returns {Object} Kết quả cập nhật
   */
  async handleIncomingDocument(reportData) {
    try {
      const incomingCount = reportData.metrics?.incomingDocuments || 0;
      if (incomingCount <= 0) return null;

      const unitId = reportData.unit;
      const result = await DocumentMetadata.incrementIncomingDocuments(unitId, incomingCount);

      // Cập nhật thông tin report liên quan
      if (result && reportData._id) {
        await DocumentMetadata.findByIdAndUpdate(result._id, {
          'lastUpdateInfo.reportId': reportData._id
        });
      }

      console.log(`[DocumentMetadataService] Incremented incoming documents: ${incomingCount} for unit: ${unitId}`);
      return result;

    } catch (error) {
      console.error('[DocumentMetadataService] Error handling incoming document:', error);
      throw error;
    }
  }

  /**
   * Cập nhật counter khi có công văn được trả lời
   * @param {Object} reportData - Dữ liệu report
   * @returns {Object} Kết quả cập nhật
   */
  async handleRepliedDocument(reportData) {
    try {
      const repliedCount = reportData.metrics?.repliedDocuments || 0;
      if (repliedCount <= 0) return null;

      const unitId = reportData.unit;
      const result = await DocumentMetadata.incrementRepliedDocuments(unitId, repliedCount);

      // Cập nhật thông tin report liên quan
      if (result && reportData._id) {
        await DocumentMetadata.findByIdAndUpdate(result._id, {
          'lastUpdateInfo.reportId': reportData._id
        });
      }

      console.log(`[DocumentMetadataService] Incremented replied documents: ${repliedCount} for unit: ${unitId}`);
      return result;

    } catch (error) {
      console.error('[DocumentMetadataService] Error handling replied document:', error);
      throw error;
    }
  }

  /**
   * Lấy số lượng công văn chưa trả lời
   * @param {String} unitId - ID đơn vị (optional)
   * @returns {Number} Số lượng công văn chưa trả lời
   */
  async getUnansweredDocumentsCount(unitId = null) {
    try {
      return await DocumentMetadata.getUnansweredCount(unitId);
    } catch (error) {
      console.error('[DocumentMetadataService] Error getting unanswered documents count:', error);
      return 0;
    }
  }

  /**
   * Lấy thông tin chi tiết metadata
   * @param {String} unitId - ID đơn vị (optional)
   * @returns {Object} Thông tin metadata
   */
  async getMetadataInfo(unitId = null) {
    try {
      const filter = {
        type: 'unanswered_documents_counter',
        deletedAt: { $exists: false }
      };

      if (unitId) {
        filter.unit = unitId;
      } else {
        filter.unit = { $exists: false };
      }

      const metadata = await DocumentMetadata.findOne(filter).lean();

      if (!metadata) {
        // Nếu chưa có metadata, khởi tạo
        const initResult = await this.initializeCounter(unitId);
        return initResult.data;
      }

      return {
        unitId: unitId,
        totalIncomingDocuments: metadata.counters.totalIncomingDocuments,
        totalRepliedDocuments: metadata.counters.totalRepliedDocuments,
        unansweredDocuments: metadata.counters.unansweredDocuments,
        lastUpdated: metadata.lastUpdated,
        lastUpdateInfo: metadata.lastUpdateInfo,
        metadata: metadata.metadata
      };

    } catch (error) {
      console.error('[DocumentMetadataService] Error getting metadata info:', error);
      throw error;
    }
  }

  /**
   * Xử lý khi có report được tạo mới
   * @param {Object} reportData - Dữ liệu report
   */
  async handleReportCreated(reportData) {
    try {
      if (reportData.jobType !== this.DOCUMENT_JOB_TYPE_ID) {
        return; // Không phải report công văn
      }

      // Xử lý công văn đến
      if (reportData.metrics?.incomingDocuments > 0) {
        await this.handleIncomingDocument(reportData);
      }

      // Xử lý công văn trả lời
      if (reportData.metrics?.repliedDocuments > 0) {
        await this.handleRepliedDocument(reportData);
      }

    } catch (error) {
      console.error('[DocumentMetadataService] Error handling report created:', error);
    }
  }

  /**
   * Xử lý khi có report được cập nhật
   * @param {Object} oldReportData - Dữ liệu report cũ
   * @param {Object} newReportData - Dữ liệu report mới
   */
  async handleReportUpdated(oldReportData, newReportData) {
    try {
      if (newReportData.jobType !== this.DOCUMENT_JOB_TYPE_ID) {
        return; // Không phải report công văn
      }

      // Tính toán sự thay đổi
      const oldIncoming = oldReportData.metrics?.incomingDocuments || 0;
      const newIncoming = newReportData.metrics?.incomingDocuments || 0;
      const incomingDiff = newIncoming - oldIncoming;

      const oldReplied = oldReportData.metrics?.repliedDocuments || 0;
      const newReplied = newReportData.metrics?.repliedDocuments || 0;
      const repliedDiff = newReplied - oldReplied;

      // Cập nhật counter nếu có thay đổi
      if (incomingDiff !== 0) {
        await DocumentMetadata.incrementIncomingDocuments(newReportData.unit, incomingDiff);
      }

      if (repliedDiff !== 0) {
        await DocumentMetadata.incrementRepliedDocuments(newReportData.unit, repliedDiff);
      }

    } catch (error) {
      console.error('[DocumentMetadataService] Error handling report updated:', error);
    }
  }
}

module.exports = new DocumentMetadataService();
