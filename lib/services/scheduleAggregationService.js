const _ = require('lodash');
const moment = require('moment');

// Models
const WorkSchedule = require('../models/workSchedule');
const MeetingSchedule = require('../models/meetingSchedule');
const DutyShift = require('../models/dutyShift');

/**
 * Service tổng hợp lịch làm việc từ 3 nguồn: work, duty, meeting
 * Nhóm theo ngày và sắp xếp theo thời gian
 */
class ScheduleAggregationService {

  /**
   * Lấy lịch tổng hợp được nhóm theo ngày
   * @param {string} userId - ID của user
   * @param {Object} params - Tham số filter
   * @returns {Object} Kết quả tổng hợp
   */
  async getGroupedSchedule(userId, params = {}) {
    try {
      const { startDate, endDate, scheduleType } = params;

      // Tính toán khoảng thời gian
      const dateRange = this.calculateDateRange(startDate, endDate);

      // Lấy dữ liệu từ 3 nguồn song song
      const [workSchedules, dutyShifts, meetingSchedules] = await Promise.all([
        this.getWorkSchedules(userId, dateRange, scheduleType),
        this.getDutyShifts(userId, dateRange, scheduleType),
        this.getMeetingSchedules(userId, dateRange, scheduleType)
      ]);

      // Merge và chuẩn hóa dữ liệu
      const allSchedules = this.mergeSchedules(workSchedules, dutyShifts, meetingSchedules);

      // Nhóm theo ngày
      const groupedByDate = this.groupSchedulesByDate(allSchedules);

      // Sắp xếp trong từng ngày
      const sortedSchedules = this.sortSchedulesInEachDay(groupedByDate);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy lịch tổng hợp thành công'
        },
        data: {
          dateRange,
          totalSchedules: allSchedules.length,
          schedulesByDate: sortedSchedules,
          summary: this.generateSummary(allSchedules)
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tính toán khoảng thời gian
   * @param {string} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {string} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Object} Khoảng thời gian
   */
  calculateDateRange(startDate, endDate) {
    let start, end;

    if (startDate && endDate) {
      // Sử dụng khoảng thời gian được chỉ định
      start = moment(startDate, 'DD-MM-YYYY').startOf('day');
      end = moment(endDate, 'DD-MM-YYYY').endOf('day');
    } else {
      // Mặc định: từ thứ 2 đến Chủ nhật của tuần hiện tại
      const now = moment();
      start = now.clone().startOf('isoWeek'); // Thứ 2
      end = now.clone().endOf('isoWeek'); // Chủ nhật
    }

    return {
      startDate: start.format('DD-MM-YYYY'),
      endDate: end.format('DD-MM-YYYY'),
      startTimestamp: start.valueOf(),
      endTimestamp: end.valueOf()
    };
  }

  /**
   * Lấy lịch làm việc
   * @param {string} userId - ID của user
   * @param {Object} dateRange - Khoảng thời gian
   * @param {string} scheduleType - Loại lịch filter
   * @returns {Array} Danh sách lịch làm việc
   */
  async getWorkSchedules(userId, dateRange, scheduleType) {
    if (scheduleType && scheduleType !== 'work') {
      return [];
    }

    const query = {
      user: userId,
      status: 1
    };

    // Filter theo khoảng ngày (format DD-MM-YYYY)
    const dates = this.generateDateArray(dateRange.startDate, dateRange.endDate);
    query.date = { $in: dates };

    const schedules = await WorkSchedule.find(query)
      .populate('user', 'name idNumber avatar')
      .populate('createdBy', 'name')
      .lean();

    return schedules.map(schedule => ({
      id: schedule._id,
      type: 'work',
      title: _.includes(schedule.shifts.map(shift => shift.type), 'morning') && _.includes(schedule.shifts.map(shift => shift.type), 'afternoon')
        ? 'Làm cả ngày'
        : _.includes(schedule.shifts.map(shift => shift.type), 'morning')
          ? 'Làm ca sáng'
          : _.includes(schedule.shifts.map(shift => shift.type), 'afternoon')
            ? 'Làm ca chiều'
            : 'Làm việc',
      date: schedule.date,
      shifts: schedule.shifts,
      user: schedule.user,
      createdBy: schedule.createdBy,
      createdAt: schedule.createdAt,
      // Tính toán thời gian để sắp xếp
      sortTime: this.calculateWorkScheduleSortTime(schedule)
    }));
  }

  /**
   * Lấy lịch trực
   * @param {string} userId - ID của user
   * @param {Object} dateRange - Khoảng thời gian
   * @param {string} scheduleType - Loại lịch filter
   * @returns {Array} Danh sách lịch trực
   */
  async getDutyShifts(userId, dateRange, scheduleType) {
    if (scheduleType && scheduleType !== 'duty') {
      return [];
    }

    const query = {
      officer: userId,
      status: { $ne: 2 }, // Không lấy ca đã hủy
      startTime: { $gte: dateRange.startTimestamp },
      endTime: { $lte: dateRange.endTimestamp }
    };

    const shifts = await DutyShift.find(query)
      .populate('officer', 'name idNumber avatar')
      .populate('unit', 'name')
      .populate('assignedBy', 'name')
      .lean();

    return shifts.map(shift => ({
      id: shift._id,
      type: 'duty',
      title: shift.name || 'Trực',
      date: moment(shift.startTime).format('DD-MM-YYYY'),
      startTime: shift.startTime,
      endTime: shift.endTime,
      location: shift.locationDuty,
      unit: shift.unit,
      officer: shift.officer,
      assignedBy: shift.assignedBy,
      status: shift.status,
      forLeader: shift.forLeader,
      description: shift.description,
      notes: shift.notes,
      hasEquipment: shift.hasEquipment,
      createdAt: shift.createdAt,
      // Thời gian để sắp xếp
      sortTime: shift.startTime
    }));
  }

  /**
   * Lấy lịch họp
   * @param {string} userId - ID của user
   * @param {Object} dateRange - Khoảng thời gian
   * @param {string} scheduleType - Loại lịch filter
   * @returns {Array} Danh sách lịch họp
   */
  async getMeetingSchedules(userId, dateRange, scheduleType) {
    if (scheduleType && scheduleType !== 'meeting') {
      return [];
    }

    const query = {
      officers: userId,
      status: 1,
      startTime: {
        $gte: dateRange.startTimestamp,
        $lte: dateRange.endTimestamp
      }
    };

    const meetings = await MeetingSchedule.find(query)
      .populate('officers', 'name idNumber avatar')
      .populate('assignedBy', 'name')
      .lean();

    return meetings.map(meeting => ({
      id: meeting._id,
      type: 'meeting',
      title: meeting.topic || 'Cuộc họp',
      date: moment(meeting.startTime).format('DD-MM-YYYY'),
      startTime: meeting.startTime,
      endTime: meeting.endTime,
      topic: meeting.topic,
      content: meeting.content,
      officers: meeting.officers,
      assignedBy: meeting.assignedBy,
      attachments: meeting.attachments,
      createdAt: meeting.createdAt,
      // Thời gian để sắp xếp
      sortTime: meeting.startTime
    }));
  }

  /**
   * Merge dữ liệu từ 3 nguồn
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} dutyShifts - Lịch trực
   * @param {Array} meetingSchedules - Lịch họp
   * @returns {Array} Danh sách tổng hợp
   */
  mergeSchedules(workSchedules, dutyShifts, meetingSchedules) {
    return [...workSchedules, ...dutyShifts, ...meetingSchedules];
  }

  /**
   * Nhóm lịch theo ngày
   * @param {Array} schedules - Danh sách lịch
   * @returns {Object} Lịch được nhóm theo ngày
   */
  groupSchedulesByDate(schedules) {
    return _.groupBy(schedules, 'date');
  }

  /**
   * Sắp xếp lịch trong từng ngày theo thời gian
   * @param {Object} groupedSchedules - Lịch đã nhóm theo ngày
   * @returns {Array} Lịch đã sắp xếp
   */
  sortSchedulesInEachDay(groupedSchedules) {
    const sortedDays = [];

    // Sắp xếp các ngày theo thứ tự thời gian
    const sortedDates = Object.keys(groupedSchedules).sort((a, b) => {
      return moment(a, 'DD-MM-YYYY').valueOf() - moment(b, 'DD-MM-YYYY').valueOf();
    });

    sortedDates.forEach(date => {
      const daySchedules = groupedSchedules[date];

      // Sắp xếp lịch trong ngày theo thời gian
      const sortedSchedules = daySchedules.sort((a, b) => {
        // Lịch có thời gian xác định lên trước
        if (a.sortTime && !b.sortTime) return -1;
        if (!a.sortTime && b.sortTime) return 1;
        if (!a.sortTime && !b.sortTime) return 0;

        return a.sortTime - b.sortTime;
      });

      // Format thông tin ngày
      const momentDate = moment(date, 'DD-MM-YYYY');

      sortedDays.push({
        date: date,
        dayOfWeek: this.getDayOfWeekName(momentDate.day()),
        totalSchedules: sortedSchedules.length,
        schedules: sortedSchedules
      });
    });

    return sortedDays;
  }

  /**
   * Tạo mảng ngày từ startDate đến endDate (format DD-MM-YYYY)
   * @param {string} startDate - Ngày bắt đầu
   * @param {string} endDate - Ngày kết thúc
   * @returns {Array} Mảng các ngày
   */
  generateDateArray(startDate, endDate) {
    const dates = [];
    const start = moment(startDate, 'DD-MM-YYYY');
    const end = moment(endDate, 'DD-MM-YYYY');

    let current = start.clone();
    while (current.isSameOrBefore(end)) {
      dates.push(current.format('DD-MM-YYYY'));
      current.add(1, 'day');
    }

    return dates;
  }

  /**
   * Tính toán thời gian sắp xếp cho lịch làm việc
   * @param {Object} schedule - Lịch làm việc
   * @returns {number|null} Timestamp để sắp xếp
   */
  calculateWorkScheduleSortTime(schedule) {
    if (!schedule.shifts || schedule.shifts.length === 0) {
      return null;
    }

    // Lấy ca sớm nhất trong ngày
    const earliestShift = schedule.shifts.reduce((earliest, shift) => {
      const shiftTime = this.parseTimeString(shift.startTime);
      const earliestTime = earliest ? this.parseTimeString(earliest.startTime) : null;

      if (!earliestTime || shiftTime < earliestTime) {
        return shift;
      }
      return earliest;
    }, null);

    if (!earliestShift) {
      return null;
    }

    // Chuyển đổi thành timestamp trong ngày
    const scheduleDate = moment(schedule.date, 'DD-MM-YYYY');
    const [hours, minutes] = earliestShift.startTime.split(':').map(Number);

    return scheduleDate.clone().hour(hours).minute(minutes).valueOf();
  }

  /**
   * Parse time string thành số phút từ đầu ngày
   * @param {string} timeStr - Chuỗi thời gian (HH:mm)
   * @returns {number} Số phút từ đầu ngày
   */
  parseTimeString(timeStr) {
    if (!timeStr) return 0;

    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Lấy tên thứ trong tuần
   * @param {number} dayOfWeek - Số thứ (0 = Chủ nhật)
   * @returns {string} Tên thứ
   */
  getDayOfWeekName(dayOfWeek) {
    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    return days[dayOfWeek];
  }

  /**
   * Tạo thống kê tổng quan
   * @param {Array} schedules - Danh sách tất cả lịch
   * @returns {Object} Thống kê
   */
  generateSummary(schedules) {
    const summary = {
      total: schedules.length,
      byType: {
        work: 0,
        duty: 0,
        meeting: 0
      },
      byDate: {}
    };

    schedules.forEach(schedule => {
      // Đếm theo loại
      summary.byType[schedule.type]++;

      // Đếm theo ngày
      if (!summary.byDate[schedule.date]) {
        summary.byDate[schedule.date] = 0;
      }
      summary.byDate[schedule.date]++;
    });

    return summary;
  }
}

module.exports = new ScheduleAggregationService();
