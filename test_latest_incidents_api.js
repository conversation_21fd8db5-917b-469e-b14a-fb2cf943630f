/**
 * Test script cho API Latest Incidents
 * Chạy script này để test API endpoint mới
 *
 * <PERSON><PERSON>ch sử dụng:
 * node test_latest_incidents_api.js
 */

const axios = require('axios');

// Cấ<PERSON> hình test
const API_BASE_URL = 'http://localhost:3000'; // Thay đổi theo port server của bạn
const TEST_TOKEN = 'your-test-token-here'; // Thay bằng token thực tế

/**
 * Test API Latest Incidents
 */
async function testLatestIncidentsAPI() {
  console.log('🚀 Bắt đầu test API Latest Incidents...\n');

  try {
    // Test 1: Lấy 3 vụ việc mới nhất không có filter
    console.log('📋 Test 1: Lấy 3 vụ việc mới nhất (không filter)');
    const response1 = await axios.post(`${API_BASE_URL}/api/v1.0/statistics/latest-incidents`, {}, {
      headers: {
        'token': TEST_TOKEN,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Response Status:', response1.status);
    console.log('📊 Response Data:', JSON.stringify(response1.data, null, 2));
    console.log('📈 Số vụ việc trả về:', response1.data.data?.total || 0);
    console.log('');

    // Test 2: Lấy vụ việc với filter theo khu vực (nếu có area ID)
    const testAreaId = '507f1f77bcf86cd799439011'; // Thay bằng area ID thực tế
    console.log('📋 Test 2: Lấy vụ việc với filter theo khu vực');
    const response2 = await axios.post(`${API_BASE_URL}/api/v1.0/statistics/latest-incidents`, {
      area: testAreaId
    }, {
      headers: {
        'token': TEST_TOKEN,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Response Status:', response2.status);
    console.log('📊 Response Data:', JSON.stringify(response2.data, null, 2));
    console.log('📈 Số vụ việc trả về:', response2.data.data?.total || 0);
    console.log('🏢 Filter theo khu vực:', testAreaId);
    console.log('');

    // Test 3: Test với area ID không hợp lệ
    console.log('📋 Test 3: Test với area ID không hợp lệ');
    const response3 = await axios.post(`${API_BASE_URL}/api/v1.0/statistics/latest-incidents`, {
      area: 'invalid-area-id'
    }, {
      headers: {
        'token': TEST_TOKEN,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Response Status:', response3.status);
    console.log('📊 Response Data:', JSON.stringify(response3.data, null, 2));
    console.log('');

    console.log('🎉 Tất cả test cases đã hoàn thành!');

  } catch (error) {
    console.error('❌ Lỗi khi test API:', error.message);

    if (error.response) {
      console.error('📊 Response Status:', error.response.status);
      console.error('📊 Response Data:', JSON.stringify(error.response.data, null, 2));
    }

    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Không thể kết nối đến server. Hãy đảm bảo server đang chạy trên port 3000');
    }
  }
}

/**
 * Test dữ liệu mẫu để kiểm tra format response
 */
function testResponseFormat() {
  console.log('📋 Kiểm tra format response mong đợi:');

  const expectedResponse = {
    code: 200,
    data: {
      incidents: [
        {
          _id: "report_id_incident_count_1",
          title: "Số vụ việc #1",
          description: "Vụ việc Số vụ việc từ báo cáo: Tiêu đề báo cáo",
          reportId: "report_id",
          reportType: "quick",
          caseCode: "CASE001",
          workStatus: "in_progress",
          status: "submitted",
          metricKey: "incident_count",
          metricLabel: "Số vụ việc",
          metricValue: 5,
          incidentIndex: 1,
          jobType: {
            _id: "jobtype_id",
            name: "Tên loại công việc",
            description: "Mô tả loại công việc",
            chartTypes: ["highlight"]
          },
          createdBy: {
            _id: "user_id",
            name: "Tên người tạo",
            idNumber: "123456",
            units: [],
            areas: []
          },
          unit: {
            _id: "unit_id",
            name: "Tên đơn vị"
          },
          location: {
            address: "Địa chỉ",
            coordinates: [105.123, 21.456],
            areas: []
          },
          incidentTime: 1640995200000,
          createdAt: 1640995200000,
          updatedAt: 1640995200000,
          timeAgo: "2 giờ trước"
        }
      ],
      total: 1,
      maxLimit: 3,
      filter: {
        area: null,
        chartType: "highlight"
      },
      generatedAt: 1640995200000
    }
  };

  console.log(JSON.stringify(expectedResponse, null, 2));
}

// Chạy test
if (require.main === module) {
  console.log('🧪 Test API Latest Incidents');
  console.log('================================\n');

  // Hiển thị format response mong đợi
  testResponseFormat();
  console.log('\n================================\n');

  // Chạy test thực tế (comment out nếu chưa có token)
  // testLatestIncidentsAPI();

  console.log('⚠️  Để chạy test thực tế, hãy:');
  console.log('1. Cập nhật TEST_TOKEN với token hợp lệ');
  console.log('2. Đảm bảo server đang chạy');
  console.log('3. Uncomment dòng testLatestIncidentsAPI()');
}

module.exports = {
  testLatestIncidentsAPI,
  testResponseFormat
};
